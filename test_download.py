#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
"""

import requests
import json
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api():
    """测试API连接"""
    base_url = "https://api.ixunke.cn/zhangguangpu/api"
    token = "****************************************************************************************************************************************"
    qbank_id = 5
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    print("🔍 测试API连接...")
    
    # 测试章节API
    url = f"{base_url}/chapter"
    params = {
        'qBankId': qbank_id,
        'app': 'true',
        'token': token
    }
    
    try:
        print(f"📡 请求URL: {url}")
        print(f"📋 参数: {params}")
        
        response = requests.get(url, params=params, headers=headers, verify=False, timeout=30)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 响应成功")
            print(f"📄 errno: {data.get('errno')}")
            print(f"📝 errmsg: {data.get('errmsg')}")
            
            if data.get('errno') == 0:
                chapters = data.get('data', [])
                print(f"📚 获取到 {len(chapters)} 个章节")
                
                # 统计有题目的章节
                chapters_with_questions = [ch for ch in chapters if ch.get('questionCount', 0) > 0]
                print(f"📝 其中 {len(chapters_with_questions)} 个章节有题目")
                
                # 显示前几个章节
                for i, chapter in enumerate(chapters_with_questions[:5]):
                    print(f"  {i+1}. {chapter.get('title')} (ID: {chapter.get('id')}, 题目: {chapter.get('questionCount')})")
                
                if len(chapters_with_questions) > 5:
                    print(f"  ... 还有 {len(chapters_with_questions) - 5} 个章节")
                
                return True
            else:
                print(f"❌ API返回错误: {data.get('errmsg')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"📄 响应内容: {response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_question_api():
    """测试题目API"""
    base_url = "https://api.ixunke.cn/zhangguangpu/api"
    token = "****************************************************************************************************************************************"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    print("\n🔍 测试题目API...")
    
    # 使用一个测试章节ID（从现有数据中获取）
    test_chapter_id = 45  # 试听课章节
    
    url = f"{base_url}/question"
    params = {
        'qBankId': 5,
        'chapterId': test_chapter_id,
        'page': 1,
        'pageSize': 10,
        'app': 'true',
        'token': token
    }
    
    try:
        print(f"📡 请求URL: {url}")
        print(f"📋 参数: {params}")
        
        response = requests.get(url, params=params, headers=headers, verify=False, timeout=30)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 响应成功")
            print(f"📄 errno: {data.get('errno')}")
            
            if data.get('errno') == 0:
                question_data = data.get('data', {})
                questions = question_data.get('list', [])
                total = question_data.get('total', 0)
                
                print(f"📝 获取到 {len(questions)} 道题目 (总共 {total} 道)")
                
                if questions:
                    first_question = questions[0]
                    print(f"📋 第一题示例:")
                    print(f"  ID: {first_question.get('id')}")
                    print(f"  内容: {first_question.get('content', '')[:100]}...")
                    print(f"  答案: {first_question.get('answer', '')}")
                
                return True
            else:
                print(f"❌ API返回错误: {data.get('errmsg')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🎓 API连接测试工具")
    print("=" * 50)
    
    # 测试章节API
    chapter_success = test_api()
    
    # 测试题目API
    if chapter_success:
        question_success = test_question_api()
        
        if question_success:
            print("\n🎉 所有API测试通过！可以开始下载章节数据。")
        else:
            print("\n⚠ 题目API测试失败，请检查token或网络连接。")
    else:
        print("\n❌ 章节API测试失败，请检查token或网络连接。")
    
    print("\n" + "=" * 50)
