{"count": 50, "totalPages": 1, "pageSize": 50, "currentPage": 1, "data": {"questions": [{"id": 4942, "type": "1", "stem": "<p>【教材章节练习】-项目进度计划</p>\n<p>(  )提供详尽的计划，说明项目如何以及何时交付项目范围中定义的产品、服务和成果，是一种用于沟通和管理干系人期望的工具，为绩效报告提供了依据。</p>\n", "answer": [0], "analysis": "<p>P323,进度管理计划是项目管理计划的组成部分，为编制、监督和控制项目进度建立准则和明确活动要求。</p>\n<p>项目进度计划是进度模型的输出，为各个相互关联的活动标注了计划日期、持续时间、里程碑和所需资源等。</p>\n", "options": ["<p>项目进度计划</p>\n", "<p>进度管理计划</p>\n", "<p>项目章程</p>\n", "<p>项目管理计划</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1111, "rightCount": 833, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 3, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4943, "type": "1", "stem": "<p>【教材章节练习】-进度管理计划</p>\n<p>关于进度管理计划的理解，不正确的是(  )。</p>\n", "answer": [1], "analysis": "<p>P301,进度管理计划可以是正式或非正式的，非常详细或腔概括的。</p>\n", "options": ["<p>进度管理计划是项目管理计划的组成部分</p>\n", "<p>进度管理计划既可以非常详细，也可以高度概括，但必须是正式的</p>\n", "<p>进度管理计划为编制、监督和控制项目进度建立准则和明确活动</p>\n", "<p>进度管理计划会规定用于制定项目进度模型的进度规划方法论和工具</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1049, "rightCount": 853, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4944, "type": "1", "stem": "<p>【教材章节练习】-滚动式规划</p>\n<p>在早期的战略规划阶段，信息尚不够明确，工作包只能分解到已知的详细水平，而后，随着了解到更多的信息，近期即将实施的工作包就可以分解到具体的活动，该方法是(  )。</p>\n", "answer": [2], "analysis": "<p>P303,滚动式规划是一种迭代式的规划技术，即详细规划近期要完成的工作，同时在较高层级上粗略规划远期工作。它是一种渐进明细的规划方式，适用于工作包、规划包。因此，在项目生命周期的不同阶段，工作的详细程度会有所不同。在早期的战略规划阶段，信息尚不够明确，工作包只能分解到已知的详细水平;而后，随着了解到更多的信息，近期即将实施的工作包就可以分解到具体的活动。</p>\n", "options": ["<p>专家判断</p>\n", "<p>分解</p>\n", "<p>滚动式规划</p>\n", "<p>标杆管理法</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1027, "rightCount": 902, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4945, "type": "1", "stem": "<p>【教材章节练习】-活动属性</p>\n<p>关于活动属性的理解，不正确的是(  )。</p>\n", "answer": [1], "analysis": "<p>P303,活动属性是指每项活动所具有的多重属性，用来扩充对活动的描述，活动属性随着项目进展情况演进并更新。</p>\n", "options": ["<p>活动属性是指每项活动所具有的多重属性，用来扩充对活动的描述</p>\n", "<p>活动属性不会随时间而演进</p>\n", "<p>活动属性可用于识别开展工作的地点、编制开展活动的项目日历，以及相关的活动类型</p>\n", "<p>在项目初始阶段，活动属性包括唯一活动标识、WBS标识和活动名称</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 983, "rightCount": 917, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4946, "type": "1", "stem": "<p>【教材章节练习】-活动之间的关系</p>\n<p>只有在验证请求者身份合法后，服务器才开始向客户端传输数据，这是(  )关系。</p>\n", "answer": [0], "analysis": "<p>P306,活动之间的关系:完成到开始(FS:只有紧前活动完成，紧后活动才能开始的逻辑关系。例如，只有完成装配PC硬件(紧前活动),才能开始在PC上安装操作系统(紧后活动)。</p>\n<p>完成到完成FF;只有紧前活动完成，紧后活动才能完成的逻辑关系。例如，只有完成文件的编写(紧前活动),才能完成文件的编辑(紧后活动)。</p>\n<p>开始到开始(SS:只有紧前活动开始，紧后活动才能开始的逻辑关系。例如，开始地基浇灌(紧前活动),才能开始混凝土的找平(紧后活动)。</p>\n<p>开始到完成SF:只有紧前活动开始，紧后活动才能完成的逻辑关系。例如只有启动新应付账款系统(紧前活动),才能关闭旧的应付账款系统(紧后活动)。</p>\n", "options": ["<p>FS</p>\n", "<p>FF</p>\n", "<p>SS</p>\n", "<p>SF</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 988, "rightCount": 903, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4947, "type": "1", "stem": "<p>【教材章节练习】-活动之间的关系</p>\n<p>活动B可在活动A完成前5天开始，则关系表示为(  )。</p>\n", "answer": [3], "analysis": "<p>P309,活动之间的关系。</p>\n", "options": ["<p>SS+5</p>\n", "<p>SS-5</p>\n", "<p>FS+5</p>\n", "<p>FS-5</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1013, "rightCount": 809, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4948, "type": "1", "stem": "<p>【教材章节练习】-依赖关系</p>\n<p>监控系统项目中，甲方要求采用防护等级为IP67的摄像头，这属于(  )。</p>\n", "answer": [0], "analysis": "<p>P308,强制性依赖关系:强制性依赖关系是法律或合同要求的或工作内在性质决定的依赖关系，又称硬逻辑关系或硬依赖关系。强制性依赖关系往往与客观限制有关。例如，在建筑项目中，只有在地基建成后，才能建立地面结构;在电子项目中，必须先把原型制造出来，然后才能对其进行测试。</p>\n", "options": ["<p>强制性依赖关系</p>\n", "<p>选择性依赖关系</p>\n", "<p>外部依赖关系</p>\n", "<p>内部依赖关系</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 994, "rightCount": 834, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 3, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4949, "type": "1", "stem": "<p>【教材章节练习】-类比估算</p>\n<p>相对于其他估算技术，(  )通常成本较低、耗时较少，但准确性也较低。</p>\n", "answer": [0], "analysis": "<p>P312,类比估算通常成本较低、耗时较少，但准确性也较低。类比估算可以针对整个项目或项目中的某个部分进行，也可以与其他估算方法联合使用。</p>\n", "options": ["<p>类比估算</p>\n", "<p>参数估算</p>\n", "<p>三点估算</p>\n", "<p>自下而上估算</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 966, "rightCount": 892, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4950, "type": "1", "stem": "<p>【教材章节练习】-快速跟进</p>\n<p>快速跟进是一种进度压缩技术，(  )属于快速跟进技术。</p>\n", "answer": [3], "analysis": "<p>P321,快速跟进:是一种进度压缩技术，将正常情况下按顺序进行的活动或阶段改为至少是部分并行开展。例如，在大楼的建筑图纸尚未全部完成前就开始建地基。快速跟进可能造成返工和风险增加，所以它只适用于能够通过并行活动来缩短关键路径上的项目工期的情况。若进度加快而使用提前量，通常会增加相关活动之间的协调工作，并增加质量风险。快速跟进还有可能增加项目成本。</p>\n", "options": ["<p>加班</p>\n", "<p>增加项目组成员</p>\n", "<p>加快关键路径上的活动</p>\n", "<p>将正常情况下按顺序进行的活动或阶段改为至少是部分并行开展</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1007, "rightCount": 806, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4951, "type": "1", "stem": "<p>【模拟题】-规划进度管理</p>\n<p>规划项目进度管理是为实施项目进度管理制定政策、程序。并形成文档化的项目进度管理计划的过程，(  )不属于规划项目进度管理的输入。</p>\n", "answer": [2], "analysis": "<p>P300,里程碑清单是定义活动的输出，排列活动顺序的输入，不是规划进度管理的输入。</p>\n", "options": ["<p>项目章程</p>\n", "<p>范围管理计划</p>\n", "<p>里程碑清单</p>\n", "<p>组织文化</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 982, "rightCount": 759, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4952, "type": "1", "stem": "<p>【模拟题】-进度管理计划</p>\n<p>关于项目进度管理计划的描述，正确的是(  )</p>\n", "answer": [1], "analysis": "<p>P301,项目进度管理计划可以走变更流程修改，选项A错误。选项B是正确的，制定进度管理计划的输入里面有项目章程。根据项目需要，进度管理计划可以是正式或非正式的，非常详细或高度概括的，CD也是错误的</p>\n", "options": ["<p>项目进度管理计划一旦确定，不能被修改</p>\n", "<p>在制定项目进度管理计划时，应该考虑项目章程</p>\n", "<p>项目进度管理计划一定要形成正式的文件</p>\n", "<p>项目进度管理计划是详细的，不能是高度概括的</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 962, "rightCount": 835, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4953, "type": "1", "stem": "<p>【模拟题】-项目章程</p>\n<p>项目章程中规定的(  ),会影响项目的进度管理。</p>\n", "answer": [0], "analysis": "<p>P300,项目章程中规定的总体里程碑进度计划会影响项目的进度管理。</p>\n", "options": ["<p>总体里程碑进度计划</p>\n", "<p>范围基准</p>\n", "<p>风险清单</p>\n", "<p>成本基准</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 978, "rightCount": 792, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4954, "type": "1", "stem": "<p>【模拟题】-定义活动</p>\n<p>&quot;定义活动&quot;过程的输出不包括(  )。</p>\n", "answer": [1], "analysis": "<p>P302,此题考察的是定义活动的输出，必须掌握，高频考点;定义活动的输出包括:</p>\n<p>(1).活动清单;(2).活动属性;(3).里程碑清单;(4).变更请求(5).项目管理计划更新(进度基准、成本基准);范围基准是输入</p>\n", "options": ["<p>活动清单</p>\n", "<p>范围基准</p>\n", "<p>变更请求</p>\n", "<p>活动属性</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 971, "rightCount": 782, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4955, "type": "1", "stem": "<p>【模拟题】一定义活动</p>\n<p>关于进度管理的描述，不正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P303,里程碑是项目生命周期中的一个时刻，里程碑的持续时间为零，里程碑既不消耗资源也不花费成本，通常是指一个主要可交付成果的完成。</p>\n", "options": ["<p>里程碑的持续时间可以为零，也可以不为零</p>\n", "<p>活动需要消耗资源和花费成本，里程碑不需要资源和成本</p>\n", "<p>活动属性可能随着项目的推进而变化</p>\n", "<p>里程碑是项目中的重要时点或事件，里程碑清单列出了项目所有的里程碑</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 944, "rightCount": 789, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4956, "type": "1", "stem": "<p>【模拟题】-定义活动</p>\n<p>前导图法可以描述四种关键活动类型的依赖关系，对于接班同事A到岗，交班同事B才可以下班的交接班过程，可以用(  )描述。</p>\n", "answer": [0], "analysis": "<p>P306,接班同事A开始到岗-交班同事B下班完成，是开始到完成的关系。类似的例子还有新系统上线-老系统下线等</p>\n", "options": ["<p>S-F</p>\n", "<p>S-S</p>\n", "<p>F-F</p>\n", "<p>F-S</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 984, "rightCount": 706, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4957, "type": "1", "stem": "<p>【模拟题】-箭线图</p>\n<p>关于箭线图的描述不正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P307,流入同一节点的活动均有共同的后续活动。流出同一节点的活动均有共同的紧前活动。</p>\n", "options": ["<p>流入同一节点的活动均有共同的紧前活动</p>\n", "<p>任两项活动的紧前事件和紧后事件代号至少有一个不同</p>\n", "<p>网络图中每一活动和每一事件都必须有唯一的一个代号，即网络图中不会有相同的代号</p>\n", "<p>虚活动不消耗时间，也不消耗资源，主要用于表达活动之间的关系</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 968, "rightCount": 804, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4958, "type": "1", "stem": "<p>【模拟题】-排列活动顺序</p>\n<p>关于活动排序的描述，不正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P308,选择性依赖关系通常是基于具体应用领域的最佳实践或者是基于项目的某些特殊性质而设定</p>\n", "options": ["<p>除了首尾两项活动之外，每项活动和每个里程碑都至少有一项紧前活动和一项紧后活动</p>\n", "<p>在前导图法中，每项活动有唯一的活动号，每项活动都注明了预计工期(活动的持续时间)。</p>\n", "<p>在箭线图法中，虚活动可以弥补箭线图在表达活动依赖关系方面的不足</p>\n", "<p>内部依赖关系通常是基于具体应用领域的最佳实践或者是基于项目的某些特殊性质而设定</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 995, "rightCount": 661, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4959, "type": "1", "stem": "<p>【模拟题】-活动属性</p>\n<p>关于活动属性的理解，不正确的是(  )</p>\n", "answer": [1], "analysis": "<p>P303,活动属性是指每项活动所具有的多重属性，用来扩充对活动的描述，活动属性随着项目进展情况演进并更新。</p>\n", "options": ["<p>活动属性是指每项活动所具有的多重属性，用来扩充对活动的描述</p>\n", "<p>活动属性不会随时间而演进</p>\n", "<p>活动属性可用于识别开展工作的地点、编制开展活动的项目日历，以及相关的活动类型</p>\n", "<p>活动属性可能包括活动描述、紧前活动、紧后活动、逻辑关系、提前量和滞后量、资源需求、强制日期、制约因素和假设条件</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 914, "rightCount": 892, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4960, "type": "1", "stem": "<p>【模拟题】-网络图</p>\n<p>关于双代号网络图的描述，不正确的是(  )。</p>\n", "answer": [2], "analysis": "<p>P307,为了绘图的方便，人们引入了一种额外的、特殊的活动，叫做虚活动。它不消耗时间，也不占用资源，在网络图中由一个虚箭线表示。在箭线图法中，有如下3个基本原则:1网络图中每一活动和每一事件都必须有唯一的一个代号，即网络图中不会有相同的代号。</p>\n<p>2任两项活动的紧前事件和紧后事件代号至少有一个不相同，节点代号沿箭线方向越来越大。3流入(流出)同一节点的活动，均有共同的紧后活动(或紧前活动)。</p>\n", "options": ["<p>网络图中不会有相同的代号</p>\n", "<p>节点代号沿箭线方向越来越大</p>\n", "<p>流入同一节点的活动，均有共同的紧前活动</p>\n", "<p>虚活动不消耗时间，也不消耗资源，只是为了弥补箭线图在表达活动依赖关系方面的不足。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 915, "rightCount": 882, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4961, "type": "1", "stem": "<p>【模拟题】-确定和整合依赖关系</p>\n<p>在系统集成项目中，只有各个设备组件完成，团队才能对其进行测试，设备组件完成和测试活动之间属于(  )关系。</p>\n", "answer": [2], "analysis": "<p>P308,只有机器组装完毕，团队才能对其测试，这是一个内部的强制性的依赖关系。</p>\n", "options": ["<p>强制性依赖关系</p>\n", "<p>选择性依赖</p>\n", "<p>内部强制性依赖</p>\n", "<p>内部选择性依赖</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 998, "rightCount": 590, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4962, "type": "1", "stem": "<p>【模拟题】-确定和整合依赖关系</p>\n<p>关于项目活动之间依赖关系的描述，不正确的是(  )</p>\n", "answer": [1], "analysis": "<p>P308,如果打算快速跟进，应当审查相应的选择性依赖关系。</p>\n", "options": ["<p>项目团队按照领域内的最佳实践来安排活动，属于选择性依赖关系</p>\n", "<p>如果打算进行快速跟进，应当审查相应的强制性依赖关系</p>\n", "<p>软件项目的测试活动取决子外部硬件的到货、属于外部依赖关系</p>\n", "<p>外部依赖关系是项目活动与非项目活动之间的依赖关系，属于外部依赖关系</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1011, "rightCount": 468, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4963, "type": "1", "stem": "<p>【模拟题】-进度管理</p>\n<p>关于进度管理的描述，正确的是:(  )。</p>\n", "answer": [2], "analysis": "<p>P312,类比估算通常成本较低、耗时较少，但准确性也较低。A错误</p>\n<p>参数估算的准确性取决于参数模型的成熟度和基础数据的可靠性。参数估算可以针对整个项目</p>\n<p>或项目中的某个部分，并可与其他估算方法联合使用。B错误管理储备用来应对会影响项目的&quot;未知-未知&quot;风险。D错误</p>\n", "options": ["<p>类比估算以过去类似项目的数值为依据，来估算未来项目的同类参数或指标，因此准确性较高</p>\n", "<p>参数估算的准确性取决于参数模型的成熟度和基础数据的可靠性，准确性低，不能用于估算整个项目</p>\n", "<p>可以对单个活动或整个项目预留应急储备，也可以同时对单个活动和整个项目都预留应急储备</p>\n", "<p>管理储备用来应对已经核实的已识别风险，以及已经制定应急或减轻措施的已识别风险</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 925, "rightCount": 712, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4964, "type": "1", "stem": "<p>【模拟题】-制订进度计划</p>\n<p>(  )不属于制订进度计划的输入。</p>\n", "answer": [3], "analysis": "<p>P315,变更请求是输出。</p>\n", "options": ["<p>进度管理计划</p>\n", "<p>范围基准</p>\n", "<p>活动清单</p>\n", "<p>变更请求</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 959, "rightCount": 570, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4965, "type": "1", "stem": "<p>【模拟题】-资源数量</p>\n<p>关于以下说法，(  )是不正确的。</p>\n", "answer": [1], "analysis": "<p>P311,资源数量:增加资源数量，比如两倍投入资源但完成工作的时间不一定能缩短一半，因为投入资源可能会增加额外的风险，比如如果增加太多活动资源，可能会因知识传递、学习曲线、额外合作等其他相关因素而造成持续时间增加</p>\n", "options": ["<p>拖延症认为人们只有在最后一刻，即快到期限时才会全力以赴;帕金森定律认为只要还有时间，工作就会不断扩展，直到用完所有的时间</p>\n", "<p>增加资源数量，比如两倍投入资源完成工作的时间应可以缩短一半</p>\n", "<p>通过采购最新技术，制造工厂可以提高产量，而这可能会影响持续时间和资源需求</p>\n", "<p>收益递减规律:在保持其他因素不变的情况下，增加一个用于确定单位产出所需投入的因素(如资源)会最终达到一个临界点，在该点之后的产出或输出会随着增加这个因素而递减</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 905, "rightCount": 812, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4966, "type": "1", "stem": "<p>【模拟题】-参数估算</p>\n<p>在进行项目活动历时估算时，(  )属于参数估算。</p>\n", "answer": [1], "analysis": "<p>P312,参数估算是利用历史数据和一些变量之间的统计关系，计算活动资源成本。</p>\n", "options": ["<p>利用以历时信息为依据的专家判断估算</p>\n", "<p>用需要完成工作的数量乘以完成单位工作所需时间为估算活动时间的依据</p>\n", "<p>利用有可能的历时估算，最乐观的历时估算和最悲观的历时估算来计算</p>\n", "<p>从以前类似计划活动的实际持续时间为依据来估算</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 972, "rightCount": 597, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4967, "type": "1", "stem": "<p>【模拟题】-控制进度的工作内容</p>\n<p>(  )属于控制进度的工作内容。</p>\n", "answer": [3], "analysis": "<p>P327,控制进度作为实施整体变更控制过程的一部分，关注内容包括:</p>\n<p>(1)判断项目进度的当前状态;</p>\n<p>(2)对引起进度变更的因素施加影响;</p>\n<p>(3)重新考虑必要的进度储备;</p>\n<p>(4)判断项目进度是否已经发生变更;</p>\n<p>(5)在变更实际发生时对其进行管理。</p>\n", "options": ["<p>确定完成项目工作所需花费的时间量</p>\n", "<p>确定完成项目工作所需的资源</p>\n", "<p>确定工作之间的逻辑关系</p>\n", "<p>重新考虑必要的进度储备</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 969, "rightCount": 621, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 3, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4968, "type": "1", "stem": "<p>【模拟题】-制定进度计划的工具和技术</p>\n<p>关于制定进度计划的工具和技术的描述，不正确的是:(  )。</p>\n", "answer": [2], "analysis": "<p>P319,相对于资源平衡而言，资源平滑不会改变项目关键路径，完工日期也不会延迟。也就是说，活动只在其自由浮动时间和总浮动时间延迟。因此，资源平滑技术可能无法实现所有资源的优化。</p>\n", "options": ["<p>总浮动的时间等于本活动的最迟完成时间减去本活动的最早完成时间</p>\n", "<p>自由浮动时间等于紧后活动的最早开始时间的最小值减去本活动的最早完成时间</p>\n", "<p>资源平滑技术通过缩短项目的关键路径来缩短完工时间</p>\n", "<p>关键路径上活动的总浮动时间与自由浮动时间都为0</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 935, "rightCount": 756, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4969, "type": "1", "stem": "<p>【模拟题】一制定进度计划的工具和技术</p>\n<p>在制定进度计划时，可以采用多种工具与技术，如关键路径法、资源平衡技术、资源平滑技术等，在以下叙述中，不正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P319,关键路径上的活动被称为关键活动。进度网络图中可能有多条关键路径。在项目进展过程中，有的活动会提前完成，有的活动会推迟完成，有的活动会中途取消，新的活动可能会被中途加入网络图在不断变化，关键路径也在不断变化之中。</p>\n<p>资源平衡:为了在资源需求与资源供给之间取得平衡，根据资源制约对开始日期和结束日期进行调整的一种技术。如果共享资源或关键资源只在特定时间可用，数黑箭艮，或被过度分配，如一个资源在同一时段内被分配至两个或多个活动，就需要进行资源平衡。也可以为保持资源使用量处于均衡水平而进行资源平衡。资源平衡往往导致关键路径改变，通常是延长。</p>\n<p>资源平滑:对进度模型中的活动进行调整，从而使项目资源需求不超过预定的资源限制的一种技术。相对于资源平衡而言，资源平滑不会改变项目关键路径，完工日期也不会延迟。也就是说，活动只在其自由浮动时间和总浮动时间内延迟。因此，资源平滑技术可能无法实现所有资</p>\n<p>源的优化</p>\n", "options": ["<p>项目的关键路径可能有一条或多条</p>\n", "<p>随着项目的开展，关键路径法可能也随着不断变化</p>\n", "<p>资源平衡技术往往会导致关键路径延长</p>\n", "<p>资源平滑技术往往会改变项目关键路径，导致项目进度延迟</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 889, "rightCount": 778, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4970, "type": "1", "stem": "<p>【模拟题】-制定进度计划的工具和技术</p>\n<p>通过增加资源来压缩进度工期的技术称为(  )。</p>\n", "answer": [2], "analysis": "<p>P320,赶工。通过增加资源，以最小的成本增加来压缩进度工期的一种技术。</p>\n", "options": ["<p>快速跟进</p>\n", "<p>持续时间缓冲</p>\n", "<p>赶工</p>\n", "<p>提前量管理</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 899, "rightCount": 781, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4971, "type": "1", "stem": "<p>【模拟题】-控制项目进度的技术</p>\n<p>控制项目进度时常用的技术不包括(  )。</p>\n", "answer": [2], "analysis": "<p>P328,工具:1.数据分析(挣值分析、迭代燃尽图、绩效审查、趋势分析、偏差分析、假设情景分析);2.关键路径法;3.项目管理信息系统;4.资源优化;5.提前量和滞后量;6.进度压缩</p>\n", "options": ["<p>趋势分析</p>\n", "<p>挣值分析</p>\n", "<p>统计抽样</p>\n", "<p>关键路径法</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 926, "rightCount": 676, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4972, "type": "1", "stem": "<p>【2015年下半年-第36题】【改编】-进度控制</p>\n<p>项目进度控制是依据项目进度基准计划对项目的实际进度进行监控，使项目能够按时完成，以下关于项目进度控制的叙述中(  )是不正确的。</p>\n", "answer": [0], "analysis": "<p>P315,分析进度计划，确定是否存在逻辑关系冲突这是制定进度计划的关键步骤。</p>\n", "options": ["<p>分析进度计划，确定是否存在逻辑关系冲突</p>\n", "<p>对引起进度变更的因素施加影响</p>\n", "<p>重新考虑必要的进度储备</p>\n", "<p>判断项目进度的当前状态</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 908, "rightCount": 630, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4973, "type": "1", "stem": "<p>【2019年下半年-第32题】【改编】-资源平衡</p>\n<p>关于进度管理的描述，不正确的是:(  )。</p>\n", "answer": [2], "analysis": "<p>P319,资源平衡往往会导致关键路径变化，通常为变长。资源平滑不会改变项目关键路径，完工日期也不会延迟。也就是说，活动只在其自由浮动时间和总浮动时间内延迟。</p>\n", "options": ["<p>项目开展过程中，关键路径可能会发生变化</p>\n", "<p>关键路径上的活动的总浮动时间和自由浮动时间都为0</p>\n", "<p>资源平滑技术通常会导致项目关键路径变长</p>\n", "<p>关键路径可能有多条</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 863, "rightCount": 837, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4974, "type": "1", "stem": "<p>【2020年下半年-第34题】-进度控制</p>\n<p>如果一个项目的2020年SPI=0.75,CPI=0.9,此时项目经理最适合采取(  )方式来控制项目进度。</p>\n", "answer": [0], "analysis": "<p>P321,进度滞后，成本超支。B会让成本更超支。C会延长进度。D起不到效果。只有A</p>\n", "options": ["<p>快速跟进</p>\n", "<p>赶工</p>\n", "<p>资源平衡</p>\n", "<p>蒙特卡洛分析</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 937, "rightCount": 589, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4975, "type": "1", "stem": "<p>【2021年下半年-第32题】-依赖关系</p>\n<p>某个程序的两个模块，模块A实现设备的运行功能，模块B实现设备运行过程中实时监控设备状态数据的功能。则项目计划网络图中，模块A和模块B的依赖关系可表示为(  )型。</p>\n", "answer": [2], "analysis": "<p>P306,前导图法包括活动之间存在的4种类型的依赖关系:</p><p>(1)结束-开始的关系(F-S型)。前序活动结束后，后续活动才能开始。例如，只有比赛(紧前活动)结束，颁奖典礼(紧后活动)才能开始。</p><p>(2)结束-结束的关系(F-F型)。前序活动结束后，后续活动才能结束。例如，只有完成文件的编写(紧前活动),才能完成文件的编辑(紧后活动)。</p><p>(3\n)<u>开始-开始的关系(S-S型)。前序活动开始后，后续活动才能开始。例如，开始地基浇灌(紧前活动)之后，才能开始混凝土的找平(紧后活动)。根据题意表述，属于S-S型</u>。</p><p>(4)开始-结束的关系(S-F型)。前序活动开始后，后续活动才能结束。例如，只有第二位保安人员开始值班(紧前活动),第一位保安人员才能结束值班(紧后活动)。</p>", "options": ["<p>F-S</p>", "<p><span style=\"text-wrap: wrap;\">F-F</span></p>", "<p>S-S<br/></p>", "<p>S-F</p>"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 939, "rightCount": 619, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4976, "type": "1", "stem": "<p>【2021年下半年】-定义活动</p>\n<p>项目进度管理中&quot;定义活动&quot;过程的主要作用不包含(  )</p>\n", "answer": [3], "analysis": "<p>P302,定义活动过程就是识别和记录为完成项目可交付成果而需采取的所有活动。其主要作用是，将工作包分解为活动，作为对项目工作进行估算、进度规划、执行、监督和控制的基础。</p>\n", "options": ["<p>准确地进行项目估算</p>\n", "<p>规划项目进度</p>\n", "<p>监控项目执行</p>\n", "<p>提供项目验收标准</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 987, "rightCount": 404, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4977, "type": "1", "stem": "<p>【2022年上半年-第30题】-提前量与滞后量</p>\n<p>某项目进度网络图中，活动A和B之间的依赖关系表示为SS-8天，则表明:(  )。</p>\n", "answer": [1], "analysis": "<p>P309,SS活动A和B是开始和开始得关系，+8代表滞后量，-8代表提前量，所以综合为活动A开始8天前活动B开始。</p>\n", "options": ["<p>活动A开始8天后活动B开始</p>\n", "<p>活动A开始8天前活动B开始</p>\n", "<p>活动A结束8天后活动B开始</p>\n", "<p>活动A结束8天前活动B开始</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 903, "rightCount": 757, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4978, "type": "1", "stem": "<p>【2023年上半年-第30题】-活动网络图</p>\n<p>关于活动排序的描述，不正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P309页。提前量是相对于紧前活动，紧后活动可提前的时间量，提前量一般用负值表示。滞后量是相对于紧前活动，紧后活动需要推迟的时间量，滞后量一般用正值表示。</p>\n", "options": ["<p>在单代号网络图中，每项活动有唯一的活动号，每项活动都标明了活动的持续时间</p>\n", "<p>双代号网络图中流入同一节点的活动，均有共同点紧后活动</p>\n", "<p>双代号网络图中，任两项活动的紧前事件和紧后事件代号至少有一个不相同</p>\n", "<p>滞后量是紧后活动相对于紧前活动需要推迟的时间量，一般用负值表示</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 873, "rightCount": 726, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4979, "type": "1", "stem": "<p>【2023年上半年-第32题】-趋势分析</p>\n<p>在控制进度过程的数据分析技术中，(  )可以通过检查项目绩效随时间的变化情况，确定绩效是在改善还是恶化。</p>\n", "answer": [2], "analysis": "<p>P328,趋势分析:旨在审查项目绩效随时间的变化情况，以判断绩效是正在改善还是正在恶化。</p>\n", "options": ["<p>储备分析</p>\n", "<p>蒙特卡洛分析</p>\n", "<p>趋势分析</p>\n", "<p>假设情景分析</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 869, "rightCount": 807, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4980, "type": "1", "stem": "<p>【2023年下半年-第2批次自编】-制定进度计划</p>\n<p>以下说法中，错误的是(  )。</p>\n", "answer": [3], "analysis": "<p>P320,</p>\n<p>(1)假设情景分析:是对各种情景进行评估，预测它们对项目目标的影响(积极或消极的)。假设情景分析就是对&quot;如果情景X出现，情况会怎样?&quot;这样的问题进行分析</p>\n<p>(2)模拟:是把单个项目风险和不确定性的其他来源模型化的方法，以评估它们对项目目标的潜在影响。最常见的技术是蒙特卡罗分析。</p>\n", "options": ["<p>亲和图可以对潜在缺陷成因进行分类，展示最应关注的领域</p>\n", "<p>直方图是一种展示数字数据的条形图，可展示每个可交付成果的缺陷数量、缺陷成因排列、各个过程的不合规次数，或项目或产品缺陷的其他表现形式</p>\n", "<p>横道图也称为&quot;甘特图&quot;,是展示进度信息的一种图表方式</p>\n", "<p>假设情景分析是把单个项目风险和不确定性的其他来源模型化的方法，以评估它们对项目目标的潜在影响。最常见的技术是蒙特卡罗分析</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 915, "rightCount": 619, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4981, "type": "1", "stem": "<p>【2023年下半年-第2批次自编】-进度活动</p>\n<p>在(  )规定可以开展进度活动的可用工作日和工作班次，它把可用于开展进度活动的时间段(按天或更小的时间单位)与不可用的时间段区分开来。</p>\n", "answer": [0], "analysis": "<p>P324,在项目日历中规定可以开展进度活动的可用工作日和工作班次，它把可用于开展进度活动的时间段(按天或更小的时间单位)与不可用的时间段区分开来。</p>\n", "options": ["<p>项目日历</p>\n", "<p>资源日历</p>\n", "<p>项目团队派工单</p>\n", "<p>物质资源分配单</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 918, "rightCount": 659, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4982, "type": "1", "stem": "<p>【2023年下半年-第4批次】-项目进度计划</p>\n<p>(  )不属于项目进度计划的图形表示方式。</p>\n", "answer": [1], "analysis": "<p>P323。项目进度计划可以是概括的或详细的。虽然项目进度计划可用列表形式，但图形方式更直观，可以采用的图形方式包括:横道图、里程碑图、项目进度网络图。</p>\n", "options": ["<p>横道图</p>\n", "<p>矩阵图</p>\n", "<p>项目进度网络图</p>\n", "<p>里程碑图</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 874, "rightCount": 769, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4983, "type": "1", "stem": "<p>【2023年下半年-第4批次】-控制进度</p>\n<p>项目进度管理过程中，可用于控制进度过程的数据分析技术包括(  )。</p>\n", "answer": [3], "analysis": "<p>P328。可用作控制进度过程的数据分析技术主要包括:挣值分析、迭代燃尽图、绩效审查、趋势分析、偏差分析、假设情景分析。</p>\n", "options": ["<p>蒙特卡洛分析</p>\n", "<p>计划评审技术(PERT)</p>\n", "<p>参数估算</p>\n", "<p>挣值分析</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 952, "rightCount": 590, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4984, "type": "1", "stem": "<p>【2023年下半年-第4批次】-估算持续时间</p>\n<p>一位软件工程师在进行软件项目持续时间估算时，按照&quot;代码行&quot;为单位进行估算，采用的估算方法是(  )。</p>\n", "answer": [2], "analysis": "<p>这是基于项目的某些参数来估算项目持续时间的方法。题目中明确提到了&quot;按照'代码行'为单位进行估算&quot;,这正是基于一个参数(即代码行)来进行的估算，所以C选项符合题目的描述。</p>\n", "options": ["<p>三点估算</p>\n", "<p>类比估算</p>\n", "<p>参数估算</p>\n", "<p>专家判断</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 896, "rightCount": 703, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4985, "type": "1", "stem": "<p>【2024年上半年-第1批次】-定义活动输出</p>\n<p>关于定义活动过程的描述，不正确的是(  )。</p>\n", "answer": [1], "analysis": "<p>P303,里程碑是项目中的重要时点或事件，里程碑清单列出了项目所有的里程碑，并指明每个里程碑是强制性的(如合同要求的)还是选择性的(如根据历史信息确定的)。里程碑的持续时间为零，因为它们代表的只是一个重要时间点或事件。</p>\n", "options": ["<p>定义活动主要由项目经理主导，也需要其他项目团队成员参与</p>\n", "<p>定义活动输出的里程碑清单是合同要求的强制性的重要时间节点和事件</p>\n", "<p>定义活动完成后，应形成一份详细的活动清单</p>\n", "<p>定义活动过程中必须考虑项目的时间限制和预算约束</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 975, "rightCount": 508, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4986, "type": "1", "stem": "<p>【2024年上半年-第1批次】-排列活动顺序工具与技术</p>\n<p>(  )不属于排列活动顺序过程的工具与技术。</p>\n", "answer": [2], "analysis": "<p>P305.P309,排列活动顺序过程的工具与技术:1.紧前关系绘图法、2.箭线图法、3.确定和整合依赖关系、4.提前量和滞后量、5.项目管理信息系统</p>\n", "options": ["<p>提前量和滞后量</p>\n", "<p>紧前关系绘图法</p>\n", "<p>关键路径法</p>\n", "<p>箭线图法</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 962, "rightCount": 504, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4987, "type": "1", "stem": "<p>【2024年上半年-第1批次】-控制进度工具与技术</p>\n<p>可用于控制进度过程的数据分析技术不包括(  )。</p>\n", "answer": [3], "analysis": "<p>P328,可用作控制进度过程的数据分析技术主要包括:挣值分析、迭代燃尽图、绩效审查、趋势分析、偏差分析、假设情景分析。</p>\n", "options": ["<p>迭代燃尽图</p>\n", "<p>偏差分析</p>\n", "<p>趋势分析</p>\n", "<p>备选方案分析</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 884, "rightCount": 709, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4988, "type": "1", "stem": "<p>【2024年上半年-第1批次】-资源平衡</p>\n<p>在一个复杂的信息系统项目中，由于资源限制，项目经理发现关键资源(如资深开发人员)经常在不同的项目任务之间被重新分配，这导致项目进度频繁延误和质量波动。为了有效控制资源并减少对进度延迟和成本超支的影响，项目经理优先采取的措施是(  )。</p>\n", "answer": [0], "analysis": "<p>P319,资源平衡是为了在资源需求与资源供给之间取得平衡，根据资源制约因素对开始日期和完成日期进行调整的一种技术。如果共享资源或关键资源只在特定时间可用而且数量有限，如一个资源在同一时段内被分配至两个或多个活动，就需要进行资源平衡。也可以为保持资源使用量处于均衡水平而进行资源平衡。</p>\n", "options": ["<p>实施资源平衡策略，优化不同任务间的资源分配</p>\n", "<p>缩减项目范围以匹配可用资源的能力</p>\n", "<p>增加资源储备以应对未来的资源需求波动</p>\n", "<p>严格限制资源在项目间的共享，确保资源专注于当前任务</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 927, "rightCount": 701, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4989, "type": "1", "stem": "<p>【2024年上半年-第2批次】-估算活动持续时间工具与技术</p>\n<p>关于估算活动持续时间过程的工具与技术的描述，不正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P312,类比估算是一种使用相似活动或项目的历史数据来估算当前活动或项目的持续时间或成本的技术。类比估算以过去类似项目的参数值(如持续时间、预算、规模、重量和复杂性等)为基础，来估算当前和未来项目的同类参数或指标。这是一种粗略的估算方法，有时需要根据项目复杂性方面的已知差异进行调整，在项目详细信息不足时，经常使用类比估算来估算项目待续时间。</p>\n<p>相对于其他估算技术，类比估算通常成本较低、耗时较少，但准确性也较低。类比估算可以针对整个项目或项目中的某个部分进行，也可以与其他估算方法联合使用。如果以往活动是本质上而不是表面上类似，并且从事估算的项目团队成员具备必要的专业知识，那么类比估算可靠性会比较高。</p>\n", "options": ["<p>类比估算是一种精确的估算方法，适用于项目详细信息充分、项目需求明确的情况</p>\n", "<p>专家判断依赖于专家的可用性和经验，可能受到主观判断的影响</p>\n", "<p>三点估算考虑了估算中的不确定性和风险，有助于界定活动持续时间的近似区间</p>\n", "<p>参数估算是一种基于历史数据和项目参数，使用某种算法来计算成本或持续时间的估算技术</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 851, "rightCount": 795, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4990, "type": "1", "stem": "<p>【2024年上半年-第2批次】-进度管理计划</p>\n<p>关于进度管理计划的概述，不正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P301,进度管理计划是项目管理计划的组成部分，为编制、监督和控制项目进度建立准则和明确活动要求。</p>\n", "options": ["<p>进度管理计划既可以是正式的也可以是非正式的，既可以是非常详细的也可以是高度概括的</p>\n", "<p>项目管理计划中规定的偏差临界值，可以用于监督进度绩效，通常用偏离基准计划中的参数的某个百分数来表示</p>\n", "<p>在采用适应型生命周期时，应指定进度管理计划发布，规划和迭代的固定时间段</p>\n", "<p>进度管理计划描述如何定义、制定、监督、控制和确认项目范围，是项目管理计划的组成部分</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 942, "rightCount": 514, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4991, "type": "1", "stem": "<p>【2024年上半年-第2批次】-进度管理</p>\n<p>关于进度管理的描述，不正确的是(  )。</p>\n", "answer": [2], "analysis": "<p>相对于资源平衡而言，资源平滑不会改变项目的关键路径，完工日期也不会延迟。也就是说，活动只在其自由和总浮动时间内延迟，但资源平滑技实现所有资源的优化。</p>\n", "options": ["<p>项目开展过程中，关键路径可能会发生变化</p>\n", "<p>关键路径上的活动的总浮动时间和自由浮动时间都为0</p>\n", "<p>资源平滑技术通常会导致项目关键路径变长</p>\n", "<p>定义活动、排列活动顺序、估算活动持续时间可以由一个人在较短时间内完成</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 857, "rightCount": 782, "meanScoreRate": 0, "qBankId": 5, "chapterId": 85, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}], "title": "第十章 项目进度管理", "lastQNo": 0, "lastQId": 0, "optional": {"singleQShowType": 2, "imgNoEvents": 0, "qEncrypt": {"switch": 0, "key": ""}, "deflate": 0, "questionComment": 1, "serverTime": *************, "ad": {}, "wltest": 2, "showStructure": 0}}, "errno": 0, "errmsg": ""}