<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节题库下载工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
            color: #666;
        }
        
        .log-container {
            margin-top: 30px;
            display: none;
        }
        
        .log-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success {
            color: #28a745;
        }
        
        .log-error {
            color: #dc3545;
        }
        
        .log-warning {
            color: #ffc107;
        }
        
        .log-info {
            color: #17a2b8;
        }
        
        .result-summary {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .chapter-list {
            margin-top: 20px;
            display: none;
        }
        
        .chapter-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chapter-info {
            flex: 1;
        }
        
        .chapter-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .chapter-meta {
            font-size: 0.9em;
            color: #666;
        }
        
        .chapter-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-downloading {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 章节题库下载工具</h1>
            <p>从API获取章节信息并下载各个章节的题库数据</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <ul>
                    <li>输入有效的API访问令牌（Token）</li>
                    <li>点击"获取章节列表"查看可用章节</li>
                    <li>点击"开始下载"下载所有章节的题目</li>
                    <li>下载的JSON文件将以章节名称命名</li>
                    <li>支持断点续传和错误重试</li>
                </ul>
            </div>
            
            <div class="form-group">
                <label for="tokenInput">API访问令牌 (Token):</label>
                <input type="text" id="tokenInput" placeholder="请输入API访问令牌..." 
                       value="VTJGc2RHVmtYMStrcGFhU2VveVAzUlJET2ZXUEVQOW03aDZRVW51Nkl6NnJvYWhRMFFteFUwa0tjT2F5L0M5ZlUwS3hPN1Q4c2YzNGVOdWlSMmlMZXc9PSMxNzUzNTk2MzYzMDQy">
            </div>
            
            <div class="form-group">
                <button class="btn" id="getChaptersBtn" onclick="getChapters()">获取章节列表</button>
                <button class="btn btn-success" id="downloadBtn" onclick="startDownload()" disabled>开始下载</button>
                <button class="btn btn-danger" id="stopBtn" onclick="stopDownload()" disabled style="display: none;">停止下载</button>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>
            
            <div class="chapter-list" id="chapterList">
                <h3>📋 章节列表</h3>
                <div id="chapterItems"></div>
            </div>
            
            <div class="log-container" id="logContainer">
                <h3>📝 下载日志</h3>
                <div class="log-box" id="logBox"></div>
            </div>
            
            <div class="result-summary" id="resultSummary"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let chapters = [];
        let isDownloading = false;
        let downloadedCount = 0;
        let totalQuestions = 0;
        let failedChapters = [];
        
        // API配置
        const API_BASE = 'https://api.ixunke.cn/zhangguangpu/api';
        const QBANK_ID = 5;
        
        // 获取章节列表
        async function getChapters() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                alert('请输入API访问令牌');
                return;
            }

            const btn = document.getElementById('getChaptersBtn');
            btn.disabled = true;
            btn.textContent = '获取中...';

            // 显示日志容器
            document.getElementById('logContainer').style.display = 'block';
            addLog('🔍 开始获取章节列表...', 'info');

            try {
                const url = `${API_BASE}/chapter?qBankId=${QBANK_ID}&app=true&token=${encodeURIComponent(token)}`;
                addLog(`📡 请求URL: ${url}`, 'info');

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9'
                    },
                    mode: 'cors'
                });

                addLog(`📊 响应状态: ${response.status}`, 'info');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                addLog(`📄 响应数据: errno=${data.errno}, errmsg=${data.errmsg || '无'}`, 'info');

                if (data.errno === 0) {
                    const allChapters = data.data || [];
                    addLog(`📚 获取到 ${allChapters.length} 个总章节`, 'info');

                    chapters = flattenChapters(allChapters);
                    addLog(`📝 其中 ${chapters.length} 个章节包含题目`, 'success');

                    if (chapters.length > 0) {
                        displayChapters(chapters);
                        document.getElementById('downloadBtn').disabled = false;
                        addLog(`✅ 章节列表获取成功！`, 'success');
                    } else {
                        addLog(`⚠ 没有找到包含题目的章节`, 'warning');
                    }
                } else {
                    addLog(`❌ API返回错误: ${data.errmsg || '未知错误'}`, 'error');
                }
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                console.error('获取章节列表失败:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '获取章节列表';
            }
        }
        
        // 扁平化章节列表
        function flattenChapters(chapterList) {
            const flattened = [];
            
            function processChapter(chapter) {
                if (chapter.questionCount > 0) {
                    flattened.push(chapter);
                }
                if (chapter.children && chapter.children.length > 0) {
                    chapter.children.forEach(processChapter);
                }
            }
            
            chapterList.forEach(processChapter);
            return flattened;
        }
        
        // 显示章节列表
        function displayChapters(chapterList) {
            const container = document.getElementById('chapterItems');
            container.innerHTML = '';
            
            chapterList.forEach((chapter, index) => {
                const item = document.createElement('div');
                item.className = 'chapter-item';
                item.innerHTML = `
                    <div class="chapter-info">
                        <div class="chapter-title">${chapter.title}</div>
                        <div class="chapter-meta">ID: ${chapter.id} | 题目数量: ${chapter.questionCount}</div>
                    </div>
                    <div class="chapter-status status-pending" id="status-${chapter.id}">等待中</div>
                `;
                container.appendChild(item);
            });
            
            document.getElementById('chapterList').style.display = 'block';
        }
        
        // 开始下载
        async function startDownload() {
            if (chapters.length === 0) {
                alert('请先获取章节列表');
                return;
            }
            
            isDownloading = true;
            downloadedCount = 0;
            totalQuestions = 0;
            failedChapters = [];
            
            // 更新UI
            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('stopBtn').style.display = 'inline-block';
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('logContainer').style.display = 'block';
            
            addLog('🎓 开始下载章节题库...', 'info');
            
            // 逐个下载章节
            for (let i = 0; i < chapters.length && isDownloading; i++) {
                const chapter = chapters[i];
                await downloadChapter(chapter, i + 1, chapters.length);
                
                // 更新进度
                const progress = ((i + 1) / chapters.length) * 100;
                updateProgress(progress, `已处理 ${i + 1}/${chapters.length} 个章节`);
                
                // 请求间隔
                if (i < chapters.length - 1 && isDownloading) {
                    await sleep(1000);
                }
            }
            
            // 完成下载
            finishDownload();
        }
        
        // 下载单个章节
        async function downloadChapter(chapter, current, total) {
            const statusElement = document.getElementById(`status-${chapter.id}`);
            statusElement.className = 'chapter-status status-downloading';
            statusElement.textContent = '下载中';

            addLog(`[${current}/${total}] 正在下载: ${chapter.title} (ID: ${chapter.id})`, 'info');
            addLog(`  预期题目数量: ${chapter.questionCount}`, 'info');

            try {
                const questions = await getChapterQuestions(chapter.id);

                if (questions && questions.length > 0) {
                    const chapterData = {
                        chapter_id: chapter.id,
                        chapter_title: chapter.title,
                        total_questions: questions.length,
                        download_time: new Date().toISOString(),
                        questions: questions
                    };

                    downloadJSON(chapterData, `${sanitizeFilename(chapter.title)}.json`);

                    statusElement.className = 'chapter-status status-success';
                    statusElement.textContent = `完成 (${questions.length}题)`;

                    downloadedCount++;
                    totalQuestions += questions.length;

                    addLog(`  ✅ 成功下载 ${questions.length} 道题目`, 'success');
                } else {
                    statusElement.className = 'chapter-status status-error';
                    statusElement.textContent = '无题目';
                    failedChapters.push(chapter.title);
                    addLog(`  ⚠ 未获取到题目 (可能是权限限制或章节为空)`, 'warning');
                }
            } catch (error) {
                statusElement.className = 'chapter-status status-error';
                statusElement.textContent = '失败';
                failedChapters.push(chapter.title);
                addLog(`  ❌ 下载失败: ${error.message}`, 'error');
            }
        }
        
        // 获取章节题目
        async function getChapterQuestions(chapterId) {
            const token = document.getElementById('tokenInput').value.trim();
            const allQuestions = [];
            let page = 1;
            const pageSize = 100;

            while (true) {
                try {
                    const response = await fetch(`${API_BASE}/question?qBankId=${QBANK_ID}&chapterId=${chapterId}&page=${page}&pageSize=${pageSize}&app=true&token=${encodeURIComponent(token)}`);
                    const data = await response.json();

                    if (data.errno !== 0) {
                        addLog(`    ⚠ 获取第 ${page} 页失败: errno=${data.errno}, errmsg=${data.errmsg || '未知错误'}`, 'warning');
                        break;
                    }

                    const questions = data.data?.list || [];
                    if (questions.length === 0) {
                        break;
                    }

                    allQuestions.push(...questions);
                    addLog(`    📄 已获取第 ${page} 页: ${questions.length} 道题目`, 'info');

                    const total = data.data?.total || 0;
                    if (allQuestions.length >= total) {
                        break;
                    }

                    page++;
                    await sleep(500); // 分页请求间隔
                } catch (error) {
                    addLog(`    ❌ 获取第 ${page} 页时出错: ${error.message}`, 'error');
                    break;
                }
            }

            return allQuestions;
        }
        
        // 停止下载
        function stopDownload() {
            isDownloading = false;
            document.getElementById('stopBtn').disabled = true;
            addLog('⏹ 用户停止下载', 'warning');
            finishDownload();
        }
        
        // 完成下载
        function finishDownload() {
            isDownloading = false;
            
            // 更新UI
            document.getElementById('downloadBtn').disabled = false;
            document.getElementById('stopBtn').style.display = 'none';
            updateProgress(100, '下载完成');
            
            // 显示结果摘要
            showResultSummary();
            
            // 下载摘要文件
            downloadSummary();
            
            addLog('🎉 下载任务完成!', 'success');
        }
        
        // 显示结果摘要
        function showResultSummary() {
            const summary = document.getElementById('resultSummary');
            const isSuccess = downloadedCount > 0;
            
            summary.className = `result-summary ${isSuccess ? 'result-success' : 'result-error'}`;
            summary.innerHTML = `
                <h3>${isSuccess ? '✅ 下载完成' : '❌ 下载失败'}</h3>
                <p>成功下载: ${downloadedCount}/${chapters.length} 个章节</p>
                <p>总题目数量: ${totalQuestions} 道</p>
                ${failedChapters.length > 0 ? `<p>失败章节: ${failedChapters.join(', ')}</p>` : ''}
            `;
            summary.style.display = 'block';
        }
        
        // 下载摘要文件
        function downloadSummary() {
            const summary = {
                download_time: new Date().toISOString(),
                total_chapters: chapters.length,
                success_count: downloadedCount,
                failed_count: failedChapters.length,
                total_questions: totalQuestions,
                failed_chapters: failedChapters,
                chapters_info: chapters.map(ch => ({
                    id: ch.id,
                    title: ch.title,
                    questionCount: ch.questionCount
                }))
            };
            
            downloadJSON(summary, 'download_summary.json');
        }
        
        // 工具函数
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        function addLog(message, type = 'info') {
            const logBox = document.getElementById('logBox');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logBox.appendChild(entry);
            logBox.scrollTop = logBox.scrollHeight;
        }
        
        function sanitizeFilename(filename) {
            return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
        }
        
        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>
