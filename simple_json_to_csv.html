<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化JSON转CSV工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .result-area {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
            display: none;
        }
        
        .preview-area {
            margin-top: 20px;
            display: none;
        }
        
        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .preview-table th,
        .preview-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .preview-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 简化JSON转CSV工具</h1>
        
        <div class="info-box">
            <h3>💡 使用说明</h3>
            <p><strong>输出格式：</strong>题目、选项A、选项B、选项C、选项D、答案、答案解析</p>
            <p><strong>支持格式：</strong>自动识别JSON中的题目数据结构</p>
        </div>
        
        <div class="form-group">
            <label for="fileInput">选择JSON文件:</label>
            <input type="file" id="fileInput" accept=".json" onchange="handleFileSelect(event)">
        </div>
        
        <div>
            <button onclick="convertToCSV()" id="convertBtn" disabled>转换为CSV</button>
            <button onclick="clearAll()">清空</button>
        </div>
        
        <div class="result-area" id="resultArea"></div>
        
        <div class="preview-area" id="previewArea">
            <h3>📋 数据预览 (前5条)</h3>
            <table class="preview-table" id="previewTable"></table>
        </div>
        
        <div class="log-area" id="logArea"></div>
    </div>

    <script>
        let currentFile = null;
        let csvData = null;
        
        function addLog(message) {
            const logArea = document.getElementById('logArea');
            logArea.style.display = 'block';
            logArea.innerHTML += `[${new Date().toLocaleTimeString()}] ${message}<br>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearAll() {
            currentFile = null;
            csvData = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('convertBtn').disabled = true;
            document.getElementById('resultArea').style.display = 'none';
            document.getElementById('previewArea').style.display = 'none';
            document.getElementById('logArea').style.display = 'none';
            document.getElementById('logArea').innerHTML = '';
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            if (!file.name.endsWith('.json')) {
                alert('请选择JSON格式的文件');
                return;
            }
            
            currentFile = file;
            document.getElementById('convertBtn').disabled = false;
            addLog(`已选择文件: ${file.name}`);
        }
        
        function convertToCSV() {
            if (!currentFile) {
                alert('请先选择JSON文件');
                return;
            }
            
            const btn = document.getElementById('convertBtn');
            btn.disabled = true;
            btn.textContent = '转换中...';
            
            addLog('开始读取JSON文件...');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    addLog('解析JSON数据...');
                    const jsonData = JSON.parse(e.target.result);
                    
                    addLog('提取题目数据...');
                    const questions = extractQuestions(jsonData);
                    
                    if (questions.length === 0) {
                        showResult(false, '未找到题目数据');
                        return;
                    }
                    
                    addLog(`找到 ${questions.length} 道题目`);
                    
                    addLog('转换为CSV格式...');
                    csvData = convertToCSVFormat(questions);
                    
                    addLog('生成数据预览...');
                    showPreview(questions.slice(0, 5));
                    
                    showResult(true, `成功转换 ${questions.length} 道题目`);
                    
                } catch (error) {
                    addLog(`错误: ${error.message}`);
                    showResult(false, `转换失败: ${error.message}`);
                } finally {
                    btn.disabled = false;
                    btn.textContent = '转换为CSV';
                }
            };
            
            reader.onerror = function() {
                addLog('文件读取失败');
                showResult(false, '文件读取失败');
                btn.disabled = false;
                btn.textContent = '转换为CSV';
            };
            
            reader.readAsText(currentFile, 'utf-8');
        }
        
        function extractQuestions(jsonData) {
            let questions = [];
            
            // 尝试不同的数据结构
            if (jsonData.data && jsonData.data.questions) {
                questions = jsonData.data.questions;
            } else if (jsonData.data && Array.isArray(jsonData.data)) {
                questions = jsonData.data;
            } else if (jsonData.questions) {
                questions = jsonData.questions;
            } else if (jsonData.list) {
                questions = jsonData.list;
            } else if (Array.isArray(jsonData)) {
                questions = jsonData;
            } else if (jsonData.stem || jsonData.content) {
                questions = [jsonData];
            }
            
            return questions;
        }
        
        function convertToCSVFormat(questions) {
            const headers = ['题目', '选项A', '选项B', '选项C', '选项D', '答案', '答案解析'];
            let csv = headers.join(',') + '\n';

            questions.forEach(question => {
                const row = processQuestion(question);
                const csvRow = headers.map(header => {
                    const value = row[header] || '';
                    return '"' + value.replace(/"/g, '""') + '"';
                }).join(',');
                csv += csvRow + '\n';
            });

            return csv;
        }
        
        function processQuestion(question) {
            // 提取题目
            const stem = cleanHTML(question.stem || question.content || '');

            // 提取选项
            const options = question.options || [];
            const separatedOptions = separateOptions(options);

            // 提取答案
            const answer = question.answer || '';
            const formattedAnswer = formatAnswer(answer);

            // 提取解析
            const analysis = cleanHTML(question.analysis || question.explanation || '');

            return {
                '题目': stem,
                '选项A': separatedOptions.A,
                '选项B': separatedOptions.B,
                '选项C': separatedOptions.C,
                '选项D': separatedOptions.D,
                '答案': formattedAnswer,
                '答案解析': analysis
            };
        }
        
        function cleanHTML(text) {
            if (!text) return '';
            return text.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
        }
        
        function separateOptions(options) {
            const result = {
                A: '',
                B: '',
                C: '',
                D: ''
            };

            if (!options || options.length === 0) return result;

            const labels = ['A', 'B', 'C', 'D'];

            options.forEach((option, index) => {
                if (index < labels.length) {
                    const clean = cleanHTML(option);
                    result[labels[index]] = clean;
                }
            });

            return result;
        }
        
        function formatAnswer(answer) {
            if (!answer) return '';
            
            const labels = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            if (Array.isArray(answer)) {
                return answer.map(idx => 
                    (typeof idx === 'number' && idx >= 0 && idx < labels.length) ? labels[idx] : idx
                ).join(', ');
            }
            
            if (typeof answer === 'number' && answer >= 0 && answer < labels.length) {
                return labels[answer];
            }
            
            return cleanHTML(String(answer));
        }
        
        function showPreview(questions) {
            const previewArea = document.getElementById('previewArea');
            const table = document.getElementById('previewTable');
            
            let html = '<tr><th>题目</th><th>选项A</th><th>选项B</th><th>选项C</th><th>选项D</th><th>答案</th><th>答案解析</th></tr>';

            questions.forEach(question => {
                const row = processQuestion(question);
                html += `<tr>
                    <td title="${row['题目']}">${row['题目'].substring(0, 30)}${row['题目'].length > 30 ? '...' : ''}</td>
                    <td title="${row['选项A']}">${row['选项A'].substring(0, 20)}${row['选项A'].length > 20 ? '...' : ''}</td>
                    <td title="${row['选项B']}">${row['选项B'].substring(0, 20)}${row['选项B'].length > 20 ? '...' : ''}</td>
                    <td title="${row['选项C']}">${row['选项C'].substring(0, 20)}${row['选项C'].length > 20 ? '...' : ''}</td>
                    <td title="${row['选项D']}">${row['选项D'].substring(0, 20)}${row['选项D'].length > 20 ? '...' : ''}</td>
                    <td>${row['答案']}</td>
                    <td title="${row['答案解析']}">${row['答案解析'].substring(0, 30)}${row['答案解析'].length > 30 ? '...' : ''}</td>
                </tr>`;
            });
            
            table.innerHTML = html;
            previewArea.style.display = 'block';
        }
        
        function showResult(success, message) {
            const resultArea = document.getElementById('resultArea');
            resultArea.className = `result-area ${success ? 'success' : 'error'}`;
            
            if (success) {
                resultArea.innerHTML = `
                    <strong>✅ ${message}</strong><br>
                    <button class="btn btn-success" onclick="downloadCSV()" style="margin-top: 10px;">
                        📥 下载CSV文件
                    </button>
                `;
            } else {
                resultArea.innerHTML = `<strong>❌ ${message}</strong>`;
            }
            
            resultArea.style.display = 'block';
        }
        
        function downloadCSV() {
            if (!csvData || !currentFile) {
                alert('没有可下载的数据');
                return;
            }
            
            try {
                // 添加BOM以支持Excel中文显示
                const csvContent = '\ufeff' + csvData;
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                
                // 生成文件名
                const fileName = currentFile.name.replace('.json', '.csv');
                
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.style.display = 'none';
                
                // 添加到页面并点击
                document.body.appendChild(a);
                a.click();
                
                // 清理
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                addLog(`文件 ${fileName} 下载成功`);
                
            } catch (error) {
                addLog(`下载失败: ${error.message}`);
                alert('下载失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
