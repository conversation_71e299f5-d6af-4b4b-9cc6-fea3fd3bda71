﻿题目,选项A,选项B,选项C,选项D,答案,答案解析
"【2024年上半年-第1题】 关于项目可行性研究的描述不正确的是( )。","初步可行性研究报告，必须包含项目的主要投资支出","初步可行性研究报告的核心内容不包括项目进度安排","初步的可行性研究报告可作为正式文件，支持项目决策","初步可行性研究是详细可行性研究的基础","B, 15","包括从需求、设计、开发、安装实施到运营的所有设备与材料的投入分析。(3)空间 布局:如网络规划、物理布局方案的选择。(4)项目设计:包括项目总体规划、信息系统设 计和设备计划、网络工程规划等。(5)项目进度安排:包括项目整体周期、里程碑阶段划分 等。(6)项目投资与成本估算:包括投资估算、成本估算、资金渠道及初步筹集方案等。"
"【2024年上半年-第2题】 某项目的项目经理在成本控制过程中，发现成本偏差(CV) 呈现负值，为了采取有效的纠正措 施，优先策略是( )。","降低项目质量要求，以实现更高的成本效益","识别并减少冗余活动的工作量，以降低成本","减少合同交付内容，以减少成本负担","增加项目范围，以提高收入来降低成本超支","B","CV&lt;0, 说明成本超支了，所以选择B是最合适的。ACD都需要客户或发起人确认之后 才可以实施。"
"【2024年上半年-第3题】 在一个信息化系统开发项目中， ( )属于有效的项目绩效度量指标。","项目经理加班工时","系统可用性百分比","项目文档的总页数","开发进度报告更新频率","B","P543, 绩效度量指标:描述与系统运行相关的物理或功能属性，例如尺寸、重量、容 量、准确度、可靠性和效率等。"
"【2024年上半年-第4题】 适用于识别风险过程的数据分析技术是( )。","核查单","头脑风暴","SWOT技术","访谈","C","P446, 识别风险过程的数据分析技术中包括:根本原因分析、假设条件和制约因素分 析、SWOT分析、文件分析。"
"【2024年上半年-第5题】 在规划质量管理过程中，确定项目和产品质量标准时的正确做法是( )。","使用决策分析识别达到项目目标所需的关键活动","使用回归分析展示项目团队成员的技能分布","使用流程图分析评估项目成本与质量之间的关系","使用因果图监控项目过程中的质量和性能表现","C","P366, 流程图:流程图也称过程图，用来显示将一个或多个输入转化成一个或多个输 出的过程中，所需步骤顺序和可能分支。流程图有助于了解和估算一个过程的质量成本，通过 工作流的逻辑分支及其相对频率来估算质量成本，这些逻辑分支细分为完成符合要求的输出而 需要开展的一致性工作和非一致性工作。故C 选项正确。多标准决策分析有助于排定质量测量 指标的优先顺序。回归分析和因果图不属于规划质量管理的工具技术。"
"【2024年上半年-第6题】 关于项目干系人管理的描述，正确的是( )。","识别干系人过程的输出文件包括干系人登记册、工作绩效信息、变更请求","监管机构、环保人士、媒体与项目关联程度低，不需要纳入干系人管理","识别干系人参与的过程需要在整个项目期间定期开展","干系人参与计划是规划干系人参与过程的输入文件","C","P505, 识别干系人过程应根据需要在整个项目期间定期开展。所以C 对 。 识别干系人过程的输出没有工作绩效信息，所以A错 。 与项目有关联的人员都属于项目干系人管理的范畴。所以B 错 。 干系人参与计划是规划干系人参与的输出文件。所以D 错。"
"【2024年上半年-第7题】 迭代型与增量型项目生命周期的特点是( )。","需求在交付期间频繁细化，在交付期间实时把变更融入项目","需求在交付期间定期细化，定期把变更融入项目","需求在开发前确定，尽量限制变更","针对最终可交付成果制定可交付计划，在项目结束时一次性交付最终产品","B","P216, 各生命周期之间的联系与区别 +——————-----+——————-----+——————-----+ | &gt; 预测型 | &gt; 迭代型与增量型 | &gt; 适应型 | +——————-----+——————-----+——————-----+ | &gt; 需求在开发前预先确定 | &gt; 需求在交付期间定期细化 | &gt; 需求在交付期间频繁细化 | +——————-----+——————-----+——————-----+ +——————-----+——————-----+——————-----+ | 针对最终可交付成果制订交 | &gt; 分次交付整体项目或产品的 | &gt; 频繁交付对客户有价值的各 | | | | | | | &gt; 各个子集 | &gt; 个子集 | | &gt; 付计划，然后在项目结束时一 | | | | | | | | &gt; 次交付最终产品 | | | +——————-----+——————-----+——————-----+ | &gt; 尽量限制变更 | &gt; 定期把变更融入项目 | &gt; 在交付期间实时把变更融入 | | | | | | | | &gt; 项 目 | +——————-----+——————-----+——————-----+ | &gt; 关键干系人在特定里程碑点 | &gt; 关键干系人定期参与 | &gt; 关键干系人持续参与 | | | | | | &gt; 参与 | | | +——————-----+——————-----+——————-----+ | &gt; 通过对基本已知的情况编制 | &gt; 通过用新信息逐渐细化计划 | &gt; 随着需求和制约因素的显现 | | | | | | &gt; 详细计划来控制风险和成本 | &gt; 来控制风险和成本 | &gt; 而控制风险和成本 | +——————-----+——————-----+——————-----+"
"【2024年上半年-第8题】 ( )不属于白盒测试方法。","功能测试","静态测试","人工检查代码逻辑","语句覆盖","A","P141, 黑盒测试也称为功能测试，所以答案选A。 白盒测试也称为结构测试，主要用于软件单元测试中。它的主要思想是，将程序看作是一个透 明的白盒，测试人员完全清楚程序的结构和处理算法，按照程序内部逻辑结构设计测试用例， 检测程序中的主要执行通路是否都能按预定要求正确工作。白盒测试方法主要有控制流测试、 数据流测试和程序变异测试等。另外，使用静态测试的方法也可以实现白盒测试。例如，使用 人工检查代码的方法来检查代码的逻辑问题，也属于白盒测试的范畴。白盒测试方法中，最常 用的技术是逻辑覆盖，即使用测试数据运行被测程序，考查对程序逻辑的覆盖程度。主要的覆 盖标准有语句覆盖、判定覆盖、条件覆盖、条件/判定覆盖、条件组合覆盖、修正的条件/ 判定覆盖和路径覆盖等。黑盒测试也称为功能测试，主要用于集成测试、确认测试和系统测试 中。"
"【2024年上半年-第9题】 关于估算活动持续时间过程的工具与技术的描述，不正确的是( )。","类比估算是一种精确的估算方法，适用于项目详细信息充分、项目需求明确的情况","专家判断依赖于专家的可用性和经验，可能受到主观判断的影响","三点估算考虑了估算中的不确定性和风险，有助于界定活动持续时间的近似区间","参数估算是一种基于历史数据和项目参数，使用某种算法来计算成本或持续时间的估算技术","A","P312, 类比估算是一种使用相似活动或项目的历史数据来估算当前活动或项目的持续 时间或成本的技术。类比估算以过去类似项目的参数值(如持续时间、预算、规模、重量和复 杂性等)为基础，来估算当前和未来项目的同类参数或指标。这是一种粗略的估算方法，有时 需要根据项目复杂性方面的已知差异进行调整，在项目详细信息不足时，经常使用类比估算来 估算项目待续时间。 相对于其他估算技术，类比估算通常成本较低、耗时较少，但准确性也较低。类比估算可 以针对整个项目或项目中的某个部分进行，也可以与其他估算方法联合使用。如果以往活动是 本质上而不是表面上类似，并且从事估算的项目团队成员具备必要的专业知识，那么类比估算 可靠性会比较高。"
"【2024年上半年-第10题】 某公司进行项目投资，项目初始固定资产投资20000万元。随后5年的投入分别是1000万元、 1500万元、2000万元、1000万元和2000万元。各年度收益分别是10000万元、12000万元、 16000万元、20000万元、21000万元，则该项目的静态投资回收期为( )。 解 析: +————--+————+——----+——---+——---+——---+——---+ | | &gt; 初始投资 | &gt; 1 | &gt; 2 | &gt; 3 | &gt; 4 | &gt; 5 | +————--+————+——----+——---+——---+——---+——---+ | &gt; 投入 | &gt; 20000 | &gt; 1000 | &gt; 1500 | &gt; 2000 | &gt; 1000 | &gt; 2000 | +————--+————+——----+——---+——---+——---+——---+ | &gt; 收益 | | &gt; 10000 | &gt; 12000 | &gt; 16000 | &gt; 20000 | &gt; 21000 | +————--+————+——----+——---+——---+——---+——---+ | &gt; 净现值 | &gt; -20000 | &gt; 9000 | &gt; 10500 | &gt; 14000 | &gt; 19000 | &gt; 19000 | +————--+————+——----+——---+——---+——---+——---+ | &gt; 累计净现值 | &gt; -20000 | &gt; -11000 | &gt; -500 | &gt; 13500 | &gt; 32500 | &gt; 51500 | +————--+————+——----+——---+——---+——---+——---+ (3.1)+(-20000+9000+10500)/14000=2.036。","2.299","2.083","2.179","2.036","D",""
"【2024年上半年-第11题】 监控变更管理过程是( )的职责。","质量经理","变更请求者","变更实施者","变更经理","D","P565, 变更管理负责人也称变更经理，通常是变更管理过程解决方案的负责人，其主 要职责包括:①负责整个变更过程方案的结果;②负责变更管理过程的监控;③负责协调相关 的资源，保障所有变更按照预定过程顺利运作;④确定变更类型，组织变更计划和日程安排; ⑤管理变更的日程安排;⑥变更实施完成之后的回顾和关闭;⑦承担变更相关责任，并且具有 相应权限;⑧可能以逐级审批形式或团队会议的形式参与变更的风险评估和审批等。"
"【2024年上半年-第12题】 信息系统战略三角包括( )。","业务战略、技术战略和组织机制","业务战略、组织机制和信息系统","技术战略、组织管理和信息系统","发展战略，技术战略和组织管理","B","P94, 信息系统战略三角突出了业务战略、信息系统和组织机制之间的必要一致性。"
"【2024年上半年-第13题】 在 IT 审计流程中，&quot;深入调查并调整审计计划&quot;,属于( )的工作内容之一。","审计准备阶段","审计终结阶段","后续审计阶段","审计实施阶段","D","P88,IT 审计实施阶段是审计人员将项目审计计划付诸实施的期间。此阶段的工作是 审计全过程的中心环节，是整个审计流程的关键阶段，关系到整个审计工作的成败。实施阶段 主要完成工作包括:①深入调查并调整审计计划;②了解并初步评估IT 内部控制;③进行符 合性测试;④进行实质性测试等。"
"【2024年上半年-第14题】 关于项目采购管理的描述，不正确的是( )","对于项目非标准化的采购需求，应使用最低成本法","选择供方，在选择供方时应有适当理由才使用，唯一来源法，且应将其视为特殊情况，","项目进度计划会影响规划采购管理过程中，采购策略的制定","章程是规划采购管理过程的输入文件","A","P479,最低成本:适用于标准化或常规采购。此类采购有成熟的实践与标准，有具体 明确的预期成果，可以用不同的成本来取得。"
"【2024年上半年-第15题】 关于项目范围管理过程的描述不正确的是( )","采用敏捷或适应型生命周期，旨在应对大量变更，需要干系人持续参与项目","在预测型项目中，只有通过正式变更控制程序才能变更项目范围基准","在预测型项目中，通过多次迭代来开发可交付成果，使用未完成项反映当前需求","在适应型或敏捷型生命周期中，在每次迭代开始时都可以定义或批准详细的范围","C","P274, 采用敏捷或适应型生命周期，旨在应对大量变更，需要干系人持续参与项目。 因此，应将适应型项目的整体范围分解为一系列拟实现的需求和拟执行的工作(有时称为产品 未完成项),通过多次迭代来开发可交付成果，并在每次迭代开始时定义和批准详细的范围。"
"【2024年上半年-第16题】 显性知识的特征不包括( )。","静态存在性","可共享性","非陈述性","客观存在性","C","P696,显性知识具有4个主要特征:①客观存在性②静态存在性③可共享性④认 知元能性。"
"【2024年上半年-第17题】 关于控制质量过程目的的描述，正确的是( )","在用户验收和最终交付之前测试产品或服务的完整性、合规性、适用性","识别项目及其可交付成果的质量要求、标准，并描述将如何证明符合质量要求","评估项目团队成员的绩效，确保项目输出敏捷、高效且满足管理层期望","着眼于项目后评估的过程，旨在高效地执行项目过程，包括遵守和满足标准","A","P373, 规划质量管理是识别项目及其可交付成果的质量要求、标准，并书面描述项目 将如何证明符合质量要求、标准的过程。故B选项错误。 在项目管理中，质量保证着眼于项目使用的过程，旨在高效地执行项目过程，包括遵守和 满足标准，向干系人保证最终产品可以满足他们的需求、期望和要求。故选项D 描述错误。 控制质量是为了评估绩效，确保项目输出完整、正确目满足客户期望，而监督和记录质量 管理活动执行结果的过程。故选项C描述错误。 控制质量过程的目的是在用户验收和最终交付之前测量产品或服务的完整性、合规性和适 用性。本过程通过测量所有步骤、属性和变量，来核实与规划阶段所描述规范的一致性和合规 性。选项A描述正确。"
"【2024年上半年-第18题】 关于管理干系人参与过程，正确的是( )。","干系人参与计划必须是正式的、详细的项目文件，以确保满足项目的需要","规划干系人参与过程是管理干系人参与过程的前置流程，不受管理干系人参与过程的影响","管理干系人参与过程的输出包括变更请求、组织过程资产、事业环境因素","管理干系人参与过程的主要作用是尽可能提高干系人的支持度、降低抵制程度","D","P509, 干系人参与计划可以是正式，可以非正式，可以概括，也可以详细，规划干系 人参与受管理干系人的影响，在通过识别干系人过程明确最初的干系人群体之后，就应该编制 第一版的干系人参与计划，然后定期更新干系人参与计划，管理干系人参与过程的输出包括变 更请求。"
"【2024年上半年-第19题】 关于项目实施整体变更控制的描述不正确的是( )","项目经理对实施变更控制过程承担最终责任","影响项目基准的变更应由CCB和客户审批","批准的变更请求都是可用于实施整体变更控制过程的输入文件","CCB 也可以审查配置管理活动，应明确规定CCB 的角色和职责","C","P265 。批准的变更请求是实施整体变更控制过程的输出。变更请求是实施整体变更控 制过程的输入。"
"【2024年上半年-第20题】 关于项目集合项目组合的描述，不正确的是( )","项目组合是为实现项目目标而组合在一起管理的项目、项目集、子项目组合和运营工作","项目集将一个大型复杂项目拆分后形成若干小项目形成的集合","项目组合经理通过项目组合的总体投资效果和实现效益来衡量项目组合的成功","项目集经理通过协调项目及组建的活动，确保项目集效益按预期实现","A","P190, 项目组合是指为实现战略目标而组合在一起管理的项目、项目集、子项目组合 和运营工作。项目组合管理是指为了实现战略目标而对一个或多个项目组合进行的集中管理。 项目组合中的项目集或项目不一定存在彼此依赖或直接相关的关联关系。"
"【2024年上半年-第21题】 ( ) 不 属 于TCP/IP 的应用层协议。","DHCP(Dynamic Host Configuration,动态主机配置协议)","FTP(File Transfer Protocol.文件传输协议)","SMTP(Simple Mail Transfer Protocol,简单邮件传输协议)","ARP(Address Resolution Protocol,地址解析协议)","D","P39, 网络层中的协议主要有IP 、ICMP(Internet Control Message Protocol,网 际控制报文协议)、 IGMP(Internet Group Management Protocol,网际组管理协议)、 ARP (Address Resolution Protocol,地址解析协议)和RARP(Reverse Address Resolution Protocol, 反向地址解析协议)等，这些协议处理信息的路由和主机地址解析。"
"【2024年上半年-第22题】 项目建议书的核心内容不包括( )。","项目可行性研究认证","项目预期成果的市场预测","项目建设的必需条件","项目的必要性","A","P225, 项目建议书应该包括的核心内容有:①项目的必要性;②项目的市场预测;③ 项目预期成果(如产品方案或服务)的市场预测;④项目建设必需的条件。"
"【2024年上半年-第23题】 项目集发起人的主要职责不包括( )。","消除项目集交付的困难和障碍","为项目组合经理进行交互，确保提供适当的资源和优先级","确保项目集目标与战略愿景保持一致","提供资金","B","P572, 项目集发起人其典型职责包括: ●为项目集提供资金，确保项目集目标与战略愿景保持一致; ●使效益实现交付; ●消除项目集管理与交付的困难和障碍。"
"【2024年上半年-第24题】 关于组织定位的描述，不正确的是( )。","组织定位包括使命、愿景和价值观，不包括产品和服务定位","组织使命是组织较长期的业务发展的总方向和总特征","组织文化为日常工作提供具体的实践方法","组织愿景描述了组织发展的目的和如何达到这个目的的理性认知","A","P642, 组织定位包括应有清晰的使命、愿景和目标，有明确的价值观和组织文化来帮 助组织实现战略要点，并能够向组织的内外部传达清晰的定位。组织定位还应包括对业务单元 的定位战略。业务单元定位战略实质上是行业或领域中的产品或服务定位战略，也就是在行业 或领域定位之后，所作出的产品定位决策或服务定位决策。"
"【2024年上半年-第25题】 关于管理沟通过程中工作绩效报告的描述，不正确的是( )。","工作绩效报告通过分析绩效测量结果得出，能够提供关于项目工作绩效的信息","工作绩效报告的典型示例包括状态报告、进度报告，是管理沟通过程的输出","工作绩效报告可以包含挣值图表、缺陷直方图、合同绩效以及风险概述信息","工作绩效报告可以表现为有助于制定决策和采取行的仪表指示图","B","P424, 工作绩效报告的典型示例包括状态报告和进展报告，是管理沟通过程的输入。"
"【2024年上半年-第26题】 关于项目质量管理的描述，不正确的是( )。","质量管理由独立的质量保证团队在项目生命周期的特定阶段执行","质量目标是落实质量方针的具体要求，从属于质量方针","质量管理是为了实现质量目标而进行的所有质量性质的活动","质量方针是由组织最高管理者正式发布的该组织总的质量宗旨和方向","A","P369, 质量管理需要在整个项目生命周期中进行。管理质量是所有人的共同职责，包 括项目经理、项目团队、项目发起人、执行组织的管理层，甚至是客户。所有人在管理项目质 量方面都扮演一定的角色，尽管这些角色的人数和工作量不同。参与质量管理工作的程度取决 于所在行业和项目管理风格。在敏捷型项目中，整个项目期间的质量管理由所有团队成员执行; 但在传统项目中，质量管理通常是特定团队成员的职责。"
"【2024年上半年-第28题】 某项目经理负责一个软件开发项目，活动和它们之间的依赖关系: 活动A: 需求分析，预计耗时2天。 活动B: 设计，依赖于活动A的完成，预计耗时3天。 活动C: 编码，依赖于活动B 的完成，预计耗时5天。 活动D: 测试，与活动C同时开展，但必须在活动C完成后才能完全结束，预计耗时7天。 活动E: 部署，依赖于活动C 和 D的完成，预计耗时2天。 根据上述活动和它们的依赖关系，项目工期为( )天。","14","12","13","19","A","2+3+7+2=14。"
"【2024年上半年-第29题】 关于信息系统生命周期的描述，不正确的是( )。","信息系统的产生、建设、运行、维护、完善构成一个循环的过程，并有一定的规律可循","信息系统建设和维护随着各种环境变化，需要不断维护和修改，必要时可由新系统替代","信息系统的生命周期可简化为系统规划、系统分析、系统设计、系统运行和维护等阶段","信息系统建设周期长、投资大、用户习惯难以改变，定制化开发后无法进行重建和升级","C","P4, 信息系统的生命周期可以简化为:系统规划(可行性分析与项目开发计划),系 统分析(需求分析),系统设计(概要设计、详细设计),系统实施(编码、测试),系统 运行和维护等阶段。"
"【2024年上半年-第30题】 制定一个数据元标准的步骤是( )。 ①界定业务范围 ②开展业务流程分析与信息建模 ③描述数据的内容，质量等信息 ④提取数据元并规范属性 ⑤发布数据元标准并维护","①②③⑤④","③①④②⑤","①④②③⑤","③①②④⑤","D","P151, 数据元制定的基本过程: (1)描述(2)界定业务范围(3)开展业务流程分 析与信息建模(4)借助于信息模型，提取数据元，并按照一定的规则规范其属性(5)对于代 码型的数据元，编制其值域，即代码表(6)与现有的国家标准或行业标准进行协调(7)发布 实施数据元标准并建立相应的动态维护管理机制"
"【2024年上半年-第31题】 实现异构数据源的数据集成，首先需要( )。","矫正数据质量","进行数据清洗","获取原始数据","实施数据标注","C","P166, 实现异构数据源的数据集成，首先要解决的问题是原始数据的提取。"
"【2024年上半年-第32题】 在确定IT 审计范围时，( )需要根据审计的目的和投入的审计成本来确定。","物理范围","组织范围","逻辑范围","总体范围","D","P81, IT审计范围的确定 +——————+————————————————--+ | &gt; IT审计范围 | &gt; 说明 | +——————+————————————————--+ | &gt; 总体范围 | &gt; 需要根据审计目的和投入的审计成本来确定 | +——————+————————————————--+ | &gt; 组织范围 | &gt; 明确审计涉及的组织机构、主要流程、活动及人员等 | +——————+————————————————--+ | &gt; 物理范围 | &gt; 具体的物理地点与边界 | +——————+————————————————--+ | &gt; 逻辑范围 | &gt; 涉及的信息系统和逻辑边界 | +——————+————————————————--+ | &gt; 其他相关内容 | | +——————+————————————————--+"
"【2024年上半年-第33题】 ( )的核心是关注IT 治理和信息化建设与数字化转型的责权利划分。","IT架构","IT技术","IT治理","IT管理","C","P69,IT治理的核心是关注IT 定位和信息化建设与数字化转型的责权利划分。"
"【2024年上半年-第34题】 ( )不属于物联网感知层设备。","温度传感器","摄像头","二维码标签","以太网交换机","D","P49, 物联网架构可分为三层:感知层、网络层和应用层。感知层由各种传感器构 成，包括温度传感器，二维码标签、 RFID标签和读写器，摄像头，GPS 等感知终端。感知层 是物联网识别物体、采集信息的来源。网络层由各种网络，包括互联网、广电网、网络管理系 统和云计算平台等组成，是整个物联网的中枢，负责传递和处理感知层获取的信息。应用层是 物联网和用户的接口，它与行业需求结合以实现物联网的智能应用。"
"【2024年上半年-第35题】 关于规划风险应对过程的概述，不正确的是( )。","如果选定的策略并不完全有效，需要制定应急计划，识别采取风险应对措施后仍然存在的未 被完全消除的次生风险","风险应对方案可以用结构化的决策技术来选择最适当的应对策略，为每个风险选择最可能有 效的策略或策略组合","对于大型或复杂的项目，可能需要以数学优化模型或实际方案分析为基础，进行备选风险应 对策略经济分析","风险应对方案应在当前项目背景下现实可行，获得全体干系人同意，并由一名责任人具体负 责","A","P458,风险应对方案应该与风险的重要性相匹配，并且能够经济有效地应对挑战，同 时在当前项目背景下现实可行，获得全体干系人的同意，并由一名责任人具体负责。往往需要 从几套可选方案中选出最优的风险应对方案，为每个风险选择最可能有效的策略或策略组合。 可用结构化的决策技术来选择最适当的应对策略;对于大型或复杂项目，可能需要以数学优化 模型或实际方案分析为基础，进行备选风险应对策略经济分析。 要为实施商定的风险应对策略制定具体的应对行动。如果选定的策略并不完全有效，或者发生了已接受的风险，就需要制订应急计划。同时，也需要识别次生风险。次生风险是实施风 险应对措施直接导致的风险。 A 选项中次生风险的描述错了。"
"【2024年上半年-第36题】 关于项目合同管理概述，不正确的( )。","经过买方认可，项目主体工作在签订分包合同时可以使用总价合同","按照项目范围划分，合同分为项目总承包合同、单项承包合同、项目分包合同","项目合同采用总价合同时，买方必须准确定义要购买的产品或服务","合同双方如签订工料合同，则买方来承担中等程度的成本风险，卖方承担单价风险","A","P493, 经合同约定和买方认可，卖方将其承包项目的某一部分或某几部分(非项目的 主体结构)再发包给具有相应资质条件的分包方，与分包方订立的合同称为项目分包合同。需 要说明的是，订立项目分包合同必须同时满足5个条件:①经过买方认可;②分包的部分必须 是项目非主体工作;③只能分包部分项目，而不能转包整个项目;④分包方必须具备相应的资 质条件;⑤分包方不能再次分包。"
"【2024年上半年-第37题】 关于确认范围管理过程的叙述，正确的是( )。","确认范围过程的作用是为所要交付的内容提供架构","符合验收标准的可交付成果应由项目经理正式签字批准","项目实施单位的管理层主要关注产品或服务范围，客户主要关注项目制约因素","项目团队成员主要关注项目范围中自己参与的元素和负责的元素","D","P289.P292, 确认范围是正式验收已完成的项目可交付成果的过程。本过程的主要作 用:①使验收过程具有客观性;②通过确认每个可交付成果来提高最终产品、服务或成果获得 验收的可能性。 · 管理层关注项目范围: 是指范围对项目的进度、资金和资源的影响，这些因素是否超过了 组织承受范围，是否在 投入产出上具有合理性。 · 客户关注产品范围: 关心项目的可交付成果是否足够完成产品或服务;在项目中，客户往 往有在当前版本中加入所有功能和特征的意愿，这对于项目来说是一种潜在的风险，会给组织 和客户带来危害和损失。 · 项目管理人员主要关注项目制约因素: 关心项目可交付成果是否足够和必须完成，时间、 资金和资源是否足够，主要的潜在风险和预备解决的方法。 · 项目团队人员主要关注项目范围中自己参与的元素和负责的元素 :通过定义范围中的时间 检查自己的工作时间是否足够，自己在项目范围中是否有多项工作。"
"【2024年上半年-第38题】 关于进度管理计划的概述，不正确的是( )。","进度管理计划既可以是正式的也可以是非正式的，既可以是非常详细的也可以是高度概括的","项目管理计划中规定的偏差临界值，可以用于监督进度绩效，通常用偏离基准计划中的参数 的某个百分数来表示","在采用适应型生命周期时，应指定进度管理计划发布，规划和迭代的固定时间段","进度管理计划描述如何定义、制定、监督、控制和确认项目范围，是项目管理计划的组成部 分","D","P301, 进度管理计划是项目管理计划的组成部分，为编制、监督和控制项目进度建立 准则和明确活动要求。"
"【2024年上半年-第39题】 用于估算成本的项目管理计划组件不包括( )。","项目章程","范围基准","成本管理计划","质量管理计划","A","P342, 用于估算成本的项目管理计划组件主要包括:成本管理计划、质量管理计划、 范围基准。"
"【2024年上半年-第40题】 结束项目或阶段过程的输入是( )。","项目最终报告","批准的产品规范","经验教训知识库","最终产品服务或成果","B","P267,ACD 均为输出，验收的可交付成果可包括批准的产品规范、交货收据和工作绩 效文件。对于分阶段实施的项目或提前取消的项目，还可能包括部分完成或中间的可交付成果。"
"【2024年上半年-第41题】 绩效评价结果的应用包含价值评价和绩效改进两层内容，( )不属于价值评价。","薪酬调整","员工奖励","在职培训","人事调动","C","P653, 绩效评价结果的应用包含两层内容:①价值评价。作为组织人事决策的重要参 考，用于相关的奖惩、薪酬调整和人事调动。②绩效改进。对绩效评估结果进行分析，为组织 安排员工培训、员工职业生涯规划等方面提供依据。"
"【2024年上半年-第42题】 关于项目整合管理的描述不正确的是( )。","项目管理计划的任何组件都可用作指导与管理项目工作的输入","批准的变更请求可能导致修改正式受控的项目文件","CCB通过对变更方案的审查决策，对变更进行管理实施和控制","组织的结构文化管理实践和可持续性是影响指导与管理项目工作过程的因素","C","P265,CCB (变更控制委员会)通常负责审查和批准变更请求，但具体的变更实施和 控制，通常是由项目团队或项目经理来执行的。 CCB 是决策机构，而不是作业机构。"
"【2024年上半年-第43题】 采购文档是采购时用于达成法律协议的各种书面文书，不包括( )。","招标文件","供方选择标准","采购工作说明书","资金筹措方案","D","P484, 采购文档是用于达成法律协议的各种书面文件，其中可能包括当前项目启动之 前的较旧文件。采购文档可包括:招标文件、采购工作说明书、独立成本估算、供方选择标准"
"【2024年上半年-第44题】 关于招标投标的描述，不正确的是( )。","属于同一集团组织成员的投标人可以按照该组织要求协同投标","招标人不得组织单个或者部分潜在投标人踏勘项目现场","国有资金占控股或主导地位的项目，可以公开招标，也可以邀请招标","一个招标项目只能有一个标底，标底必须保密","A","属于同一集团组织成员的投标人按照该组织要求协同投标属于串标。"
"【2024年上半年-第45题】 ( )不属于车联网网络连接范畴。","通过无线通信技术实现与服务平台的信息传输","人通过运营商移动网络与车辆之间进行用于控制车辆的信息沟通","人与人之间在车上通过运营商的移动网络进行通话与短信沟通。","车内设备之间进行用于对设备状态实时监测的信息数据传输","C","P11,车联网分别是车与云平台、车与车、车与路、车与人、车内设备之间等全方位 网络链接。 (1)车与云平台间的通信是指车辆通过卫星无线通信或移动蜂窝等无线通信技术实现与车 联网服务平台的信息传输，接收平台下达的控制指令，实时共享车辆数据。 (2)车与车间的通信是指车辆与车辆之间实现信息交流与信息共享，包括车辆位置、行驶 速度等车辆状态信息，可用于判断道路车流状况。 (3)车与路间的通信是指借助地面道路固定通信设施实现车辆与道路间的信息交流，用于 监测道路路面状况，引导车辆选择最佳行驶路径。 (4)车与人间的通信是指用户可以通过Wi-Fi、 蓝牙、蜂窝等无线通信手段与车辆进行信息 沟通，使用户能通过对应的移动终端设备监测并控制车辆。 (5)车内设备间的通信是指车辆内部各设备间的信息数据传输，用于对设备状态的实时检 测与运行控制，建立数字化的车内控制系统。"
"【2024年上半年-第46题】 ( )是利用复杂的算法、模型和规则，从大规模数据集中学习，以创造新的原创内容的人工智 能技术，这项技术能够创造文本、图片、声音、视频和代码等多种类型的内容全面超越了传统 软件的数据处理和分析能力。","AIGC (生成式人工智能)","CV(计算机视觉)","NLP (自然语言处理)","0CR(光学字符识别)","A","AIGC(生成式人工智能)是利用复杂的算法、模型和规则，从大规模数据集中学习， 以创造新的原创内容的人工智能技术，这项技术能够创造文本、图片、声音、视频和代码等多 种类型的内容全面超越了传统软件的数据处理和分析能力。"
"【2024年上半年-第47题】 组织开展量化管理工作的前提在于该组织已经( )。","定义了产品或项目管理的组织级标准目标","定义了组织量化过程性能目标并识别了关键过程","建立了过程性能基线及过程性能模型","建立了组织度量体系及数据收集体系","D","P590.P591, 组织开展量化管理工作的前提在于该组织已经定义了产品或项目管理的 组织级标准过程，各个产品或项目团队能够遵循组织统一的管理流程、规程和产出要求开展工 作，组织收集的度量数据才具备统计意义，可供开展量化管理建设。 CMMI模型和六西格玛均为组织级量化管理工作提供了方法和实践的指导。建立组织级的 量化管理体系的内容主要包括:定义组织量化过程性能目标、识别关键过程、建立度量体系及 数据收集、建立过程性能基线和建立过程性能模型。"
"【2024年上半年-第48题】 在数据管理领域，数据管理能力，成熟度模型 (DCMM), 将组织的管理成熟度划分为初始级、 ( )、稳健级、量化管理级和优化。","安全级","受管理级","发展级","可进阶级","B","P118, 数据管理能力成熟度模型。DCMM将组织的管理成熟度划分为5个等级，分别 是:初始级、受管理级、稳健级、量化管理级和优化级。"
"【2024年上半年-第49题】 Security vulnerability are considered a type of( ).","technical risk","management risk","organizational risk","commercial risk","A","安全漏洞被认为是一种类型。 A.技术风险 B. 管理风险 C.组织风险 D.商业风险"
"【2024年上半年-第50题】 ( )用于展示团队成员的职责分配和角色。","工作分解结构","组织分解结构","责任分配矩阵","干系人参与度评估矩阵","B","P391, 本题有争议B和 C 职责分配矩阵 (RAM), 它显示了分配给每个工作包的项目资源，用于说明工作包或活动 与项目团队成员之间的关系。 组织分解结构(0BS): 按照组织现有的部门、单元或团队排列，并在每个部门下列出项 目活动或工作包。例如，运营部门只需找到其所在的0BS位置，就能看到自己的全部项目职责。"
"【2024年上半年-第51题】 ( )标准适用于服务供方和需方确立服务内容和签署合同。","GB/T 33850《信息技术服务-质量评价指标体系》","GB/T 39770《信息技术服务-安全要求》","GB/T 37696《信息技术服务-从业人员能力评价要求》","GB/T 37961《信息技术服务-服务基本要求》","D","P728, +————-+————-+————-+————-+————-+ | &gt; 标准编号 | &gt; 标准名称 | &gt; 主要内容 | &gt; 适用范围 | &gt; 类别 | +————-+————-+————-+————-+————-+ | &gt; GB/T | &gt; 信息技术服务服 | &gt; 该标准规定了信 | &gt; 该标准适用于服 | &gt; 国家 | | &gt; 37961 | | 息技术服务中服务过程基 | 务供 | &gt; 标准 | | | &gt; 务基本要求 | | &gt; 方和需方确立服 | | | | | &gt; 本要求、信息技 | 务内 | | | | | 术咨询、设计与开发、信 | &gt; 容及签署合同* | | | | | 息 | * | | | | | &gt; 系统集成实施、 | | | | | | 运行维护、数据处理和存 | | | | | | 储、 | | | | | | &gt; 运营等服务的活 | | | | | | 动内容和成果要求 | | | +————-+————-+————-+————-+————-+"
"【2024年上半年-第52题】 为实现高效的项目组合价值，需要执行的关键活动不包括( )。","测量和报告价值","最大化价值","协商期望的价值","输出正确价值观","D","P582, 高效的项目组合价值管理需要的关键活动主要包括:协商期望的价值、最大化 价值、实现价值、测量价值和报告价值等。"
"【2024年上半年-第53题】 不能用于规划沟通管理过程的人际关系与团队技能是( )。","政策意识","冲突管理","沟通风格评估","文化意识","B","P422, 适用于规划沟通管理过程的人际关系与团队技能主要包括:沟通风格评估、政 策意识、文化意识。"
"【2024年上半年-第54题】 在项目工作绩效域，管理实物资源的主要目标不包括( )。","消除材料等待时间","杜绝报废和浪费","减少现场的材料搬运和储存","促进安全的工作环境","D","P538, 管理实物资源的目标主要包括:①减少或消除现场的材料搬运和储存;②消除 材料等待时间;③最小化报废和浪费;④促进安全的工作环境等。"
"【2024年上半年-第55题】 ( )is the basis of information.","concepts","wisdom","Data","knowledge","C","(一)是信息的基础。 A.概 念 B. 智慧 C.数 据 D.知识"
"【2024年上半年-第56题】 在招聘成本中，( )不属于显性成本。","内部推荐奖励金","发布招聘广告","增加招聘渠道","管理层参与面试","D","P673, 招聘成本是指一个职位招聘需要花费的总费用，包括显性成本和隐性成本。组 织对显性成本比较敏感，对隐性成本则认识不足。招聘成本的核算取决于多个因素，除了招聘 广告费用、内部推荐奖励资金以外，不可忽视的还有内部沟通、内部协商、管理层或技术骨干 面试等隐性成本。"
"【2024年上半年-第57题】 关于项目配置管理的描述不正确的是( )。","配置经理负责管理和决策整个项目生命周期中的配置活动","配置项负责人需要定期开展项目所有配置的审计","配置管理相关角色包括变更控制委员会，配置管理员，配置项负责人","建立和维护，配置管理系统是配置管理员的职责之一。","B","P559, 配置项负责人确保所负责的配置项的准确和真实:①记录所负责配置项的所有 变更;②维护配置项之间的关系;③调查审计中发现的配置项差异，完成差异报告;④遵从配 置管理过程;⑤参与配置管理过程评估。 配置审计是配置管理员的职责。"
"【2024年上半年-第58题】 A project is a( )endeavor undertaken to create a unique product,service,or result.","static","temporary","Permanent","renting","B","项目是为创造独特的产品、服务或成果而进行的( )努力。 A.静态 B.临 时 C.永 久 D.租 赁"
"【2024年上半年-第59题】 关于收集需求管理过程及相关技术的描述，正确的是( )。","原型法是一种结构化的头脑风暴形式，通过投票排列最有用的创意","需求跟踪矩阵是把产品需求从其来源连接到能满足需求的可交付成果的一种表格","收集需求管理过程为规划范围管理过程奠定基础，需要反复开展，贯穿于整个项目生命周期。","故事板是一种原型技术，是对产品范围的可视化描绘，可以直观显示业务系统的交互方式","B","P281, 需求跟踪矩阵是把产品需求从其来源连接到能满足需求的可交付成果的一种表 格。 名义小组技术是一种结构化的头脑风暴形式 故事板是一种原型技术，通过一系列的图像或图示来展示顺序或导航路径。 收集需求是为实现目标而确定，记录并管理干系人的需要和需求的过程。本过程的主要作用是 为定义产品范围和项目范围奠定基础。本过程仅开展一次或仅在项目的预定义点开展。"
"【2024年上半年-第60题】 适用于管理沟通过程的沟通技能不包括( )。","数据挖掘","沟通胜任力","反 馈","演 示","A","P425, 适用于管理沟通过程的沟通技能主要包括: (1)沟通胜任力。经过裁剪的沟通技能的组合，有助于明确关键信息的目的、建立有效关系、 实现信息共享和采取领导行为。 (2)反馈。反馈是关于沟通、可交付成果或情况的反应信息。反馈支持项目经理和团队及所有其他项目干系人之间的互动沟通，例如指导、辅导和磋商。 (3)非口头技能。例如，通过示意、语调和面部表情等适当的肢体语言来表达意思。镜像模 仿和眼神交流也是重要的技能。团队成员应该知道如何通过说什么和不说什么来表达自己的想 法。 (4)演示。演示是信息和文档的正式交付。向干系人明确、有效地演示项目的信息，主要 包括:①向干系人报告项目进度和信息更新;②提供背景信息以支持决策制定;③提供关于项 目及其目标的通用信息，以提升项目工作和项目团队的形象;④提供具体信息，以提升对项目 工作和目标的理解和支持力度等。"
"【2024年上半年-第61题】 关于项目风险管理过程的描述，不正确的是( )。","影响风险规划管理过程的组织过程资产是组织或关键干系人设定的整体风险的临界值","在项目实际进展中，项目风险管理过程中的各个过程会相互交叠，相互作用","实施风险应对是执行商定的风险应对计划的过程，需要在整个项目期间开展","敏捷或适应型方法管理的项目，在每个迭代期间应该识别、分析和管理风险","A","P439, 影响规划风险管理过程的事业环境因素是组织或关键干系人设定的整体风险的 临界值。故D选项错误。"
"【2024年上半年-第62题】 某企业承接了一项工程，该工作分为I 、II 、III 、IV 四项任务，甲、乙、丙、丁四个项目组 完成各项任务的时间如表所示，为实现最优配置，第IV项任务应由( )来完成。 +——--+——+——+——-+——-+ | &gt; 任务 | &gt; 甲 | &gt; 乙 | &gt; 丙 | &gt; 丁 | +——--+——+——+——-+——-+ | &gt; I | &gt; 4 | &gt; 30 | &gt; 26 | &gt; 220 | +——--+——+——+——-+——-+ | &gt; IⅡ | &gt; 20 | &gt; 8 | &gt; 28 | &gt; 200 | +——--+——+——+——-+——-+ | &gt; Ⅲ | &gt; 18 | &gt; 28 | &gt; 190 | &gt; 160 | +——--+——+——+——-+——-+ | &gt; IV | &gt; 14 | &gt; 16 | &gt; 200 | &gt; 200 | +——--+——+——+——-+——-+","甲","乙","丙","丁","C","匈牙利算法，第1步:先找出每行的最小值，然后本行数据减去最小值，得到下列表 格。 +——--+——+——+——+——+ | &gt; 任务 | &gt; 甲 | &gt; 乙 | &gt; 丙 | &gt; 丁 | +——--+——+——+——+——+ | &gt; I | &gt; 0 | &gt; 26 | &gt; 22 | &gt; 4 | +——--+——+——+——+——+ | &gt; IⅡ | &gt; 12 | &gt; 0 | &gt; 20 | &gt; 22 | +——--+——+——+——+——+ | &gt; Ⅲ | &gt; 0 | &gt; 10 | &gt; 14 | &gt; 8 | +——--+——+——+——+——+ | &gt; IV | &gt; 0 | &gt; 0 | &gt; 8 | &gt; 4 | +——--+——+——+——+——+ 第2步，上表中，找出每列的最小值，然后每列减去最小值，得到下列表格。 +——--+——+——+——+——+ | &gt; 任务 | &gt; 甲 | &gt; 乙 | &gt; 丙 | &gt; 丁 | +——--+——+——+——+——+ | &gt; I | &gt; 0 | &gt; 26 | &gt; 14 | &gt; 0 | +——--+——+——+——+——+ | &gt; IⅡ | &gt; 12 | &gt; 0 | &gt; 12 | &gt; 18 | +——--+——+——+——+——+ | &gt; Ⅲ | &gt; 0 | &gt; 10 | &gt; 6 | &gt; 4 | +——--+——+——+——+——+ | &gt; IV | &gt; 0 | &gt; 2 | &gt; 0 | &gt; 0 | +——--+——+——+——+——+ 从上表得知:I 工作可以由甲、丁完成，Ⅱ工作可由乙完成。IⅢ工作可由甲完成，IV 工作可由甲、丙、丁完 成。 综合考虑: I 由丁完成，Ⅱ由乙完成;Ⅲ由甲完成;IV由丙完成。"
"【2024年上半年-第63题】 下列关于建设团队的说法，不正确的是( )。","所有团队成员都应展现出相应的领导力和人际关系技能","出现问题或故障时能够快速定位，责任体现了团队绩效高，韧性强","团队成员相互协作与合作，有助于产生多样化的想法，获得更好成果。","团队成员对成果的主人翁意识越强，表现就越好","B","韧性:出现问题或故障时，高绩效项目团队可以快速恢复。B 选项中，出现问题第一 时间是解决问题，而不是定位责任。"
"【2024年上半年-第64题】 关于估算活动资源过程的描述不正确的是( )。","资源管理计划是估算活动资源过程的输入定义了识别项目所需不同资源的方法，还定义了量 化各活动所需的方法","资源日历是估算活动资源过程的输出识别，每种具体资源可用时间和时长","资源需求清单详细列出了未完成项目活动所需的各种资源，包括人力资源和物资等","历史信息提供以往项目中资源使用的数据和经验教训","B","P394, 资源日历:识别了每种具体资源可用时的工作日、班次、正常营业的上下班时 间、 周末和公共假期。是估算活动资源的输入。"
"【2024年上半年-第65题】 由安全机制、OSI 网络参考模型、安全服务三个轴形成的信息安全系统三维空间中，操作系统 漏洞检测与修复属于( )","通信安全","应用完全","平台安全","授权和审计安全","C","P172, 平台安全主要包括操作系统漏洞检测与修复、网络基础设施漏洞检测与修复、 通用基础应用程序漏洞检测与修复、网络安全产品部署等。"
"【2024年上半年-第66题】 关于识别干系人过程的描述，不正确的是( )","干系人登记册是识别干系人过程的主要输出记录与识别关系人的信息","对干系人进行分类的方法，包括权力利益方格、因果图、回归分析、凸显模型等","变更日志、问题日志、需求文件可以作为识别干系人过程的输入","识别干系人的主要作用是使项目团队能够建立对每个干系人或干系人群体的适度关注","B","P508, 常见的分类方法包括:(1)权力利益方格、权力影响方格，或作用影响方格 (2)干系人立方体(3)凸显模型(4)影响方向(5)优先级排序"
"【2024年上半年-第67题】 《&quot;十四五&quot;国家信息化规划》中提出了打造协同高效的数字政府体系，深入推进&quot;放管服&quot; 改革，加快政府职能转变，打造市场化、法治化、国际化营商环境，坚持整体集约建设数字政 府，推动条块政务业务协同，( )深化推进，&quot; 一 网通办&quot;&quot;跨省通办&quot;&quot; 一 网统管&quot;,畅 通参与政策制定的渠道，推动国家行政体系更加完善、政府作用更好发挥、行政效率和公信力 显著提升，推动有效市场和有为政府更好结合，打造服务型政府。","加快政务数据资产使用便捷性","加快政务数据开放共享和开发利用","加快推动政务数据的价值提升和变现","严格管控政务数据的质量和使用范围","B","P21, 《 &quot;十四五&quot;国家信息化规划》中提出了打造协同高效的数字政府体系，深入 推进&quot;放管服&quot;改革，加快政府职能转变，打造市场化、法治化、国际化营商环境，坚持整体 集约建设数字政府，推动条块政务业务协同，加快政务数据开放共享和开发利用，深化推进， &quot; 一网通办&quot;&quot;跨省通办&quot;&quot;一网统管&quot;,畅通参与政策制定的渠道，推动国家行政体系更加 完善、政府作用更好发挥、行政效率和公信力显著提升，推动有效市场和有为政府更好结合， 打造服务型政府。"
"【2024年上半年-第68、69题】 某工厂生产甲乙两种产品，每生产一件甲产品需要2吨钢材，1立方米木材，4吨水泥;每生 产一件乙产品需要3吨钢材，4立方米木材，1吨水泥。现有10吨钢材，10立方米木材，20 吨水泥。产品销售后，每件甲产品可获利1万元，每件乙产品可获利2万元，为获得最高的经 济利益，该工厂应生产(68)件甲产品，获得总利润是(69)万元。 (68)A.2 B.4 C.3 D.1 (69)A.6 B.2 C.8 D.4","","","","","A","根据题干得到如下表格: +——--+——+——+————----+ | | &gt; 甲 | &gt; 乙 | &gt; 原材料总数量 | +——--+——+——+————----+ | &gt; 钢材 | &gt; 2 | &gt; 3 | &gt; 10 | +——--+——+——+————----+ | &gt; 木材 | &gt; 1 | &gt; 4 | &gt; 10 | +——--+——+——+————----+ | &gt; 水泥 | &gt; 4 | &gt; 1 | &gt; 20 | +——--+——+——+————----+ | &gt; 利润 | &gt; 1 | &gt; 2 | | +——--+——+——+————----+ 设生产甲产品X件，乙产品Y件，根据题意，有如下不等式: 2X+3Y≤10 X+4Y≤10 4X+Y≤20 X≥0 Y≥0 目标函数:Max(X+2Y)。 可以求出X=2,Y=2, 答案就出来了。"
"【2024年上半年 -第70题】 ( )is based on chatgpt-4.","Large Language Model","Cloud Computing","Block Chain","Big Data","A","ChatGpt 是大语言模型。"
"【2024年上半年-第71题】 关于绩效实施的描述，不正确的是( )。","绩效实施的核心是持续沟通式的绩效辅导","组织管理者应当投入一定的精力进行绩效实施","绩效实施的过程是对绩效计划的执行情况的指导，监督和管理","绩效实施的具体内容包括绩效沟通和绩效评估","D","P649, 绩效实施的具体内容一般包括两个方面:一是不断的绩效沟通;二是绩效信息 的记录和收集。"
"【2024年上半年-第72题】 ( )is the correct full name for the&quot;OPM&quot; . 解 析: ( ) 是 &quot;OPM&quot; 的正确全名。 A.组织项目管理 B.组织绩效管理 C.定时投资组合管理 D.绩效管理","Organizational Project Management","0rganizational Performance Management","0ptimized Portfolio Management","0utcome Performance Management","A",""
"【2024年上半年-第73题】 在估算成本过程中，( )是该过程的输入，( )是该过程的输出。","项目范围说明书、成本管理计划","历史信息、项目预算","工作分解结构 (WBS)、 资源需求","资源需求清单、估算依据","D","P342, 资源需求清单是输入。 P344, 估算依据是输出。"
"【2024年上半年-第74题】 关于《中华人民共和国商标法》的描述和内容，不正确的是( )。","注册商标有效期满，需要继续使用的，商标注册人应在期满前12个月内按规定办理续展手 续","注册商标的有效期是十年，每次续展注册的有效期是五年","为相关公众所熟知的商标，持有人认为其权利受到侵害时，可以申请驰名 商标的保护","现行的《中华人民共和国商标法》是2019年4月修正的","B","第四十条，注册商标有效期满，需要继续使用的，商标注册人应当在期满前十二个月 内按照规定办理续展手续;在此期间未能办理的，可以给予六个月的宽展期。每次续展注册的 有效期为十年，自该商标上一届有效期满次日起计算。期满未办理续展手续的，注销其注册商 标。"
"【2024年上半年-第75题】 关于进度管理的描述，不正确的是( )。","项目开展过程中，关键路径可能会发生变化","关键路径上的活动的总浮动时间和自由浮动时间都为0","资源平滑技术通常会导致项目关键路径变长","定义活动、排列活动顺序、估算活动持续时间可以由一个人在较短时间内完成","C","相对于资源平衡而言，资源平滑不会改变项目的关键路径，完工日期也不会延迟。 也就是说，活动只在其自由和总浮动时间内延迟，但资源平滑技实现所有资源的优化。"
