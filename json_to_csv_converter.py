#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON题目数据转CSV工具
将JSON格式的题目数据转换为CSV格式，按照指定的列格式输出
"""

import json
import csv
import os
import re
from typing import Dict, List, Any, Optional
import argparse
from pathlib import Path

class JSONToCSVConverter:
    def __init__(self):
        self.output_encoding = 'utf-8-sig'  # 使用BOM以便Excel正确显示中文
        
    def clean_html(self, text: str) -> str:
        """清理HTML标签和格式化文本"""
        if not text:
            return ""
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        # 移除换行符
        text = text.replace('\n', ' ').replace('\r', ' ')
        
        return text
    
    def separate_options(self, options: List[str]) -> Dict[str, str]:
        """将选项分离为独立的字段"""
        result = {
            'A': '',
            'B': '',
            'C': '',
            'D': ''
        }

        if not options:
            return result

        option_labels = ['A', 'B', 'C', 'D']

        for i, option in enumerate(options):
            if i < len(option_labels):
                clean_option = self.clean_html(option)
                result[option_labels[i]] = clean_option

        return result
    
    def format_answer(self, answer: Any) -> str:
        """格式化答案"""
        if not answer:
            return ""
        
        # 如果答案是列表（索引形式）
        if isinstance(answer, list):
            option_labels = ['A', 'B', 'C', 'D', 'E', 'F']
            answer_letters = []
            for idx in answer:
                if isinstance(idx, int) and 0 <= idx < len(option_labels):
                    answer_letters.append(option_labels[idx])
            return ", ".join(answer_letters)
        
        # 如果答案是字符串
        elif isinstance(answer, str):
            return self.clean_html(answer)
        
        # 如果答案是数字
        elif isinstance(answer, int):
            option_labels = ['A', 'B', 'C', 'D', 'E', 'F']
            if 0 <= answer < len(option_labels):
                return option_labels[answer]
        
        return str(answer)
    
    def process_question(self, question: Dict[str, Any]) -> Dict[str, str]:
        """处理单个题目数据"""
        # 提取题目内容
        stem = self.clean_html(question.get('stem', ''))

        # 提取选项
        options = question.get('options', [])
        separated_options = self.separate_options(options)

        # 提取答案
        answer = question.get('answer', '')
        formatted_answer = self.format_answer(answer)

        # 提取解析
        analysis = self.clean_html(question.get('analysis', ''))

        return {
            '题目': stem,
            '选项A': separated_options['A'],
            '选项B': separated_options['B'],
            '选项C': separated_options['C'],
            '选项D': separated_options['D'],
            '答案': formatted_answer,
            '答案解析': analysis
        }
    
    def convert_json_to_csv(self, json_file_path: str, csv_file_path: str) -> bool:
        """将JSON文件转换为CSV文件"""
        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取题目数据
            questions = []
            
            # 处理不同的JSON结构
            if isinstance(data, dict):
                # 检查是否有data字段
                if 'data' in data:
                    data_section = data['data']
                    if isinstance(data_section, dict) and 'questions' in data_section:
                        questions = data_section['questions']
                    elif isinstance(data_section, list):
                        questions = data_section
                # 检查是否有questions字段
                elif 'questions' in data:
                    questions = data['questions']
                # 检查是否有list字段
                elif 'list' in data:
                    questions = data['list']
                # 如果直接是题目对象
                elif 'stem' in data or 'content' in data:
                    questions = [data]
            elif isinstance(data, list):
                questions = data
            
            if not questions:
                print(f"在文件 {json_file_path} 中未找到题目数据")
                return False
            
            print(f"找到 {len(questions)} 道题目")
            
            # 转换为CSV格式
            csv_data = []
            for i, question in enumerate(questions):
                try:
                    processed_question = self.process_question(question)
                    csv_data.append(processed_question)
                except Exception as e:
                    print(f"处理第 {i+1} 题时出错: {e}")
                    continue
            
            if not csv_data:
                print("没有成功处理任何题目")
                return False
            
            # 写入CSV文件
            fieldnames = ['题目', '选项A', '选项B', '选项C', '选项D', '答案', '答案解析']
            
            with open(csv_file_path, 'w', newline='', encoding=self.output_encoding) as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)
            
            print(f"成功转换 {len(csv_data)} 题目到 {csv_file_path}")
            return True
            
        except Exception as e:
            print(f"转换过程中出错: {e}")
            return False
    
    def batch_convert(self, input_dir: str, output_dir: str) -> None:
        """批量转换目录中的所有JSON文件"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找所有JSON文件
        json_files = list(input_path.glob('*.json'))
        
        if not json_files:
            print(f"在目录 {input_dir} 中未找到JSON文件")
            return
        
        success_count = 0
        total_questions = 0
        
        for json_file in json_files:
            csv_file = output_path / f"{json_file.stem}.csv"
            print(f"\n正在转换: {json_file.name}")
            
            if self.convert_json_to_csv(str(json_file), str(csv_file)):
                success_count += 1
                # 统计题目数量
                try:
                    with open(str(csv_file), 'r', encoding=self.output_encoding) as f:
                        reader = csv.reader(f)
                        next(reader)  # 跳过标题行
                        question_count = sum(1 for row in reader)
                        total_questions += question_count
                except:
                    pass
        
        print(f"\n批量转换完成:")
        print(f"✅ 成功转换: {success_count}/{len(json_files)} 个文件")
        print(f"📝 总题目数量: {total_questions} 道")
        print(f"📁 输出目录: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='JSON题目数据转CSV工具')
    parser.add_argument('--input', '-i', required=True, help='输入JSON文件或目录路径')
    parser.add_argument('--output', '-o', help='输出CSV文件或目录路径')
    parser.add_argument('--batch', '-b', action='store_true', help='批量转换模式')
    
    args = parser.parse_args()
    
    converter = JSONToCSVConverter()
    
    if args.batch:
        # 批量转换模式
        output_dir = args.output or f"{args.input}_csv"
        converter.batch_convert(args.input, output_dir)
    else:
        # 单文件转换模式
        if not args.output:
            input_path = Path(args.input)
            args.output = str(input_path.with_suffix('.csv'))
        
        converter.convert_json_to_csv(args.input, args.output)

if __name__ == "__main__":
    main()
