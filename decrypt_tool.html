<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库数据解密工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            resize: vertical;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px 0;
        }
        .log {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 3px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .download-btn {
            background-color: #28a745;
        }
        .download-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔓 题库数据解密工具</h1>
        
        <div class="section">
            <h3>📡 步骤1: 获取数据</h3>
            <button onclick="fetchData()">自动获取题库数据</button>
            <button onclick="useManualData()">使用手动数据</button>
            <div id="fetchLog" class="log" style="margin-top: 10px; height: 100px;"></div>
        </div>

        <div class="section">
            <h3>🔑 步骤2: 解密密钥</h3>
            <label>加密的密钥:</label>
            <textarea id="encryptedKey" placeholder="将自动从API获取..."></textarea>
            <label>尝试的密码 (一行一个):</label>
            <textarea id="passwords" placeholder="yuyiruankao&#10;ixunke&#10;questions&#10;qbank&#10;default&#10;123456&#10;password&#10;ruankao&#10;exam&#10;test">yuyiruankao
ixunke
questions
qbank
default
123456
password
ruankao
exam
test</textarea>
            <button onclick="decryptKey()">解密密钥</button>
            <div id="keyResult" class="log" style="margin-top: 10px; height: 80px;"></div>
        </div>

        <div class="section">
            <h3>📚 步骤3: 解密题目数据</h3>
            <label>解密后的密钥:</label>
            <input type="text" id="decryptedKey" placeholder="从步骤2获取或手动输入">
            <label>加密的题目数据:</label>
            <textarea id="encryptedQuestions" placeholder="将自动从API获取..."></textarea>
            <button onclick="decryptQuestions()">解密题目数据</button>
            <div id="questionsResult" class="log" style="margin-top: 10px; height: 100px;"></div>
        </div>

        <div class="section">
            <h3>💾 步骤4: 下载结果</h3>
            <button class="download-btn" onclick="downloadJSON()" id="downloadBtn" disabled>下载JSON文件</button>
            <button class="download-btn" onclick="downloadTXT()" id="downloadTxtBtn" disabled>下载TXT文件</button>
            <div id="downloadInfo" class="log" style="margin-top: 10px; height: 60px;"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script>
        let decryptedData = null;
        let rawData = null;

        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function fetchData() {
            clearLog('fetchLog');
            log('fetchLog', '正在获取题库数据...', 'info');

            const url = 'https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification?app=true&token=**************************************************************************************************************************************************************************%3D%3D&qBankId=4&chapterId=62&studentAnswer=1';

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                rawData = data;

                log('fetchLog', '数据获取成功!', 'success');
                log('fetchLog', `完整响应结构: ${JSON.stringify(data, null, 2)}`, 'info');

                // 调试：检查数据结构
                if (data && data.data) {
                    log('fetchLog', '✅ data.data 存在', 'success');

                    if (data.data.questions) {
                        log('fetchLog', `✅ 题目数据长度: ${data.data.questions.length}`, 'success');
                        document.getElementById('encryptedQuestions').value = data.data.questions;
                    } else {
                        log('fetchLog', '❌ data.data.questions 不存在', 'error');
                        log('fetchLog', `data.data 的键: ${Object.keys(data.data)}`, 'info');
                    }

                    if (data.data.optional && data.data.optional.qEncrypt) {
                        log('fetchLog', `✅ 加密配置: ${JSON.stringify(data.data.optional.qEncrypt)}`, 'success');
                        document.getElementById('encryptedKey').value = data.data.optional.qEncrypt.key;
                    } else {
                        log('fetchLog', '❌ 加密配置不存在', 'error');
                        if (data.data.optional) {
                            log('fetchLog', `data.data.optional 的键: ${Object.keys(data.data.optional)}`, 'info');
                        }
                    }
                } else {
                    log('fetchLog', '❌ data.data 不存在', 'error');
                    log('fetchLog', `响应的顶级键: ${Object.keys(data)}`, 'info');
                }

                log('fetchLog', '数据结构分析完成', 'info');

            } catch (error) {
                log('fetchLog', `获取数据失败: ${error.message}`, 'error');
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    log('fetchLog', '可能是CORS跨域问题，请尝试使用代理或在服务器环境中运行', 'warning');
                }
            }
        }

        function decryptCryptoJS(encryptedData, password) {
            try {
                // 使用CryptoJS解密
                const decrypted = CryptoJS.AES.decrypt(encryptedData, password);
                return decrypted.toString(CryptoJS.enc.Utf8);
            } catch (error) {
                return null;
            }
        }

        function decryptKey() {
            clearLog('keyResult');
            const encryptedKey = document.getElementById('encryptedKey').value.trim();
            const passwords = document.getElementById('passwords').value.split('\n').map(p => p.trim()).filter(p => p);
            
            if (!encryptedKey) {
                log('keyResult', '请先输入加密的密钥', 'error');
                return;
            }
            
            log('keyResult', '开始尝试解密密钥...', 'info');
            
            for (const password of passwords) {
                log('keyResult', `尝试密码: ${password}`, 'info');
                const result = decryptCryptoJS(encryptedKey, password);
                
                if (result && result.length > 0) {
                    log('keyResult', `✅ 密钥解密成功! 使用密码: ${password}`, 'success');
                    log('keyResult', `解密后的密钥: ${result}`, 'success');
                    document.getElementById('decryptedKey').value = result;
                    return;
                }
            }
            
            log('keyResult', '❌ 所有密码都尝试失败，请检查密钥或添加更多密码', 'error');
        }

        function decryptQuestions() {
            clearLog('questionsResult');
            const encryptedQuestions = document.getElementById('encryptedQuestions').value.trim();
            let decryptedKey = document.getElementById('decryptedKey').value.trim();
            
            if (!encryptedQuestions) {
                log('questionsResult', '请先输入加密的题目数据', 'error');
                return;
            }
            
            log('questionsResult', '开始解密题目数据...', 'info');
            
            // 如果没有解密的密钥，尝试使用原始密钥和常见密码
            const passwords = [];
            if (decryptedKey) {
                passwords.push(decryptedKey);
            }
            
            // 添加原始加密密钥
            const originalKey = document.getElementById('encryptedKey').value.trim();
            if (originalKey) {
                passwords.push(originalKey);
            }
            
            // 添加常见密码
            const commonPasswords = document.getElementById('passwords').value.split('\n').map(p => p.trim()).filter(p => p);
            passwords.push(...commonPasswords);
            
            for (const password of passwords) {
                log('questionsResult', `尝试密码: ${password.substring(0, 20)}...`, 'info');
                const result = decryptCryptoJS(encryptedQuestions, password);
                
                if (result && result.length > 0) {
                    try {
                        // 尝试解析为JSON
                        const jsonData = JSON.parse(result);
                        decryptedData = jsonData;
                        
                        log('questionsResult', `✅ 题目数据解密成功!`, 'success');
                        log('questionsResult', `使用密码: ${password.substring(0, 20)}...`, 'success');
                        log('questionsResult', `题目数量: ${Array.isArray(jsonData) ? jsonData.length : '未知'}`, 'success');
                        
                        // 启用下载按钮
                        document.getElementById('downloadBtn').disabled = false;
                        document.getElementById('downloadTxtBtn').disabled = false;
                        
                        log('downloadInfo', '解密成功! 可以下载文件了', 'success');
                        return;
                        
                    } catch (jsonError) {
                        // 不是JSON格式，保存为文本
                        decryptedData = result;
                        log('questionsResult', `✅ 数据解密成功! (非JSON格式)`, 'success');
                        log('questionsResult', `使用密码: ${password.substring(0, 20)}...`, 'success');
                        
                        document.getElementById('downloadTxtBtn').disabled = false;
                        log('downloadInfo', '解密成功! 可以下载TXT文件', 'success');
                        return;
                    }
                }
            }
            
            log('questionsResult', '❌ 所有密码都尝试失败', 'error');
        }

        function downloadJSON() {
            if (!decryptedData) {
                log('downloadInfo', '没有可下载的数据', 'error');
                return;
            }
            
            try {
                const jsonString = typeof decryptedData === 'string' ? decryptedData : JSON.stringify(decryptedData, null, 2);
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = 'decrypted_questions.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                log('downloadInfo', '✅ JSON文件下载成功!', 'success');
            } catch (error) {
                log('downloadInfo', `下载失败: ${error.message}`, 'error');
            }
        }

        function downloadTXT() {
            if (!decryptedData) {
                log('downloadInfo', '没有可下载的数据', 'error');
                return;
            }
            
            try {
                const textString = typeof decryptedData === 'string' ? decryptedData : JSON.stringify(decryptedData, null, 2);
                const blob = new Blob([textString], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = 'decrypted_questions.txt';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                log('downloadInfo', '✅ TXT文件下载成功!', 'success');
            } catch (error) {
                log('downloadInfo', `下载失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            log('fetchLog', '工具已准备就绪，点击"获取题库数据"开始', 'info');
        };
    </script>
</body>
</html>
