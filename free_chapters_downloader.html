<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费章节下载工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        
        .chapter-list {
            margin: 20px 0;
        }
        
        .chapter-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chapter-info {
            flex: 1;
        }
        
        .chapter-title {
            font-weight: bold;
            color: #333;
        }
        
        .chapter-meta {
            font-size: 0.9em;
            color: #666;
        }
        
        .chapter-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-free {
            background: #d4edda;
            color: #155724;
        }
        
        .status-paid {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-downloading {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 免费章节下载工具</h1>
        
        <div class="info-box">
            <h3>💡 说明</h3>
            <p>此工具专门下载免费章节的题目，避免权限问题。基于已知的免费章节列表进行下载。</p>
        </div>
        
        <div class="form-group">
            <label for="tokenInput">API访问令牌:</label>
            <input type="text" id="tokenInput" 
                   value="VTJGc2RHVmtYMStrcGFhU2VveVAzUlJET2ZXUEVQOW03aDZRVW51Nkl6NnJvYWhRMFFteFUwa0tjT2F5L0M5ZlUwS3hPN1Q4c2YzNGVOdWlSMmlMZXc9PSMxNzUzNTk2MzYzMDQy">
        </div>
        
        <div>
            <button onclick="loadFreeChapters()" id="loadBtn">加载免费章节</button>
            <button onclick="downloadFreeChapters()" id="downloadBtn" class="btn-success" disabled>下载免费章节</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="progressContainer" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备中...</div>
        </div>
        
        <div class="chapter-list" id="chapterList" style="display: none;">
            <h3>📋 免费章节列表</h3>
            <div id="chapterItems"></div>
        </div>
        
        <div class="log-area" id="logArea"></div>
    </div>

    <script>
        const API_BASE = 'https://api.ixunke.cn/zhangguangpu/api';
        const QBANK_ID = 5;
        
        // 已知的免费章节列表（基于之前的数据分析）
        const FREE_CHAPTERS = [
            { id: 45, title: "试听课", questionCount: 1, free: 1 },
            { id: 69, title: "信息系统项目管理师第四版真题", questionCount: 399, free: 1 },
            { id: 76, title: "第一章 信息化发展", questionCount: 75, free: 1 },
            { id: 77, title: "第二章 信息技术发展", questionCount: 73, free: 1 },
            { id: 78, title: "第三章 信息系统治理", questionCount: 37, free: 1 },
            { id: 79, title: "第四章 信息系统管理", questionCount: 66, free: 1 },
            { id: 81, title: "第六章 项目管理概论", questionCount: 71, free: 1 }
        ];
        
        let downloadedCount = 0;
        let totalQuestions = 0;
        let failedChapters = [];
        let isDownloading = false;
        
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        function loadFreeChapters() {
            addLog('📚 加载免费章节列表...', 'info');
            
            const container = document.getElementById('chapterItems');
            container.innerHTML = '';
            
            FREE_CHAPTERS.forEach(chapter => {
                const item = document.createElement('div');
                item.className = 'chapter-item';
                item.innerHTML = `
                    <div class="chapter-info">
                        <div class="chapter-title">${chapter.title}</div>
                        <div class="chapter-meta">ID: ${chapter.id} | 预期题目: ${chapter.questionCount}</div>
                    </div>
                    <div class="chapter-status status-free" id="status-${chapter.id}">免费</div>
                `;
                container.appendChild(item);
            });
            
            document.getElementById('chapterList').style.display = 'block';
            document.getElementById('downloadBtn').disabled = false;
            
            addLog(`✅ 已加载 ${FREE_CHAPTERS.length} 个免费章节`, 'success');
        }
        
        async function downloadFreeChapters() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                alert('请输入API访问令牌');
                return;
            }
            
            isDownloading = true;
            downloadedCount = 0;
            totalQuestions = 0;
            failedChapters = [];
            
            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('progressContainer').style.display = 'block';
            
            addLog('🚀 开始下载免费章节...', 'info');
            
            for (let i = 0; i < FREE_CHAPTERS.length; i++) {
                const chapter = FREE_CHAPTERS[i];
                await downloadChapter(chapter, i + 1, FREE_CHAPTERS.length);
                
                const progress = ((i + 1) / FREE_CHAPTERS.length) * 100;
                updateProgress(progress, `已处理 ${i + 1}/${FREE_CHAPTERS.length} 个章节`);
                
                if (i < FREE_CHAPTERS.length - 1) {
                    await sleep(1000);
                }
            }
            
            finishDownload();
        }
        
        async function downloadChapter(chapter, current, total) {
            const statusElement = document.getElementById(`status-${chapter.id}`);
            statusElement.className = 'chapter-status status-downloading';
            statusElement.textContent = '下载中';
            
            addLog(`[${current}/${total}] 下载: ${chapter.title}`, 'info');
            
            try {
                const questions = await getChapterQuestions(chapter.id);
                
                if (questions && questions.length > 0) {
                    const chapterData = {
                        chapter_id: chapter.id,
                        chapter_title: chapter.title,
                        total_questions: questions.length,
                        download_time: new Date().toISOString(),
                        questions: questions
                    };
                    
                    downloadJSON(chapterData, `${sanitizeFilename(chapter.title)}.json`);
                    
                    statusElement.className = 'chapter-status status-success';
                    statusElement.textContent = `完成 (${questions.length}题)`;
                    
                    downloadedCount++;
                    totalQuestions += questions.length;
                    
                    addLog(`  ✅ 成功: ${questions.length} 道题目`, 'success');
                } else {
                    statusElement.className = 'chapter-status status-error';
                    statusElement.textContent = '无题目';
                    failedChapters.push(chapter.title);
                    addLog(`  ⚠ 无题目`, 'warning');
                }
            } catch (error) {
                statusElement.className = 'chapter-status status-error';
                statusElement.textContent = '失败';
                failedChapters.push(chapter.title);
                addLog(`  ❌ 失败: ${error.message}`, 'error');
            }
        }
        
        async function getChapterQuestions(chapterId) {
            const token = document.getElementById('tokenInput').value.trim();
            const allQuestions = [];
            let page = 1;
            const pageSize = 100;
            
            while (true) {
                try {
                    const url = `${API_BASE}/question?qBankId=${QBANK_ID}&chapterId=${chapterId}&page=${page}&pageSize=${pageSize}&app=true&token=${encodeURIComponent(token)}`;
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'application/json, text/plain, */*'
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    if (data.errno !== 0) {
                        addLog(`    ⚠ 第${page}页失败: ${data.errmsg || '未知错误'}`, 'warning');
                        break;
                    }
                    
                    const questions = data.data?.list || [];
                    if (questions.length === 0) {
                        break;
                    }
                    
                    allQuestions.push(...questions);
                    addLog(`    📄 第${page}页: ${questions.length}题`, 'info');
                    
                    const total = data.data?.total || 0;
                    if (allQuestions.length >= total) {
                        break;
                    }
                    
                    page++;
                    await sleep(500);
                } catch (error) {
                    addLog(`    ❌ 第${page}页错误: ${error.message}`, 'error');
                    break;
                }
            }
            
            return allQuestions;
        }
        
        function finishDownload() {
            isDownloading = false;
            document.getElementById('downloadBtn').disabled = false;
            updateProgress(100, '下载完成');
            
            addLog('🎉 下载完成!', 'success');
            addLog(`📊 统计: 成功${downloadedCount}个, 总题目${totalQuestions}道`, 'success');
            
            if (failedChapters.length > 0) {
                addLog(`❌ 失败章节: ${failedChapters.join(', ')}`, 'error');
            }
            
            // 下载摘要
            const summary = {
                download_time: new Date().toISOString(),
                total_chapters: FREE_CHAPTERS.length,
                success_count: downloadedCount,
                failed_count: failedChapters.length,
                total_questions: totalQuestions,
                failed_chapters: failedChapters
            };
            
            downloadJSON(summary, 'free_chapters_summary.json');
        }
        
        function sanitizeFilename(filename) {
            return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
        }
        
        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // 页面加载完成后自动加载免费章节
        window.onload = function() {
            addLog('🚀 免费章节下载工具已加载', 'info');
            loadFreeChapters();
        };
    </script>
</body>
</html>
