# 简化的PowerShell解密脚本

# 获取API数据
$url = "https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification?app=true&token=**************************************************************************************************************************************************************************%3D%3D&qBankId=4&chapterId=62&studentAnswer=1"

Write-Host "正在获取数据..." -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri $url -Method Get
    Write-Host "数据获取成功" -ForegroundColor Green
    
    # 保存原始响应数据
    $response | ConvertTo-Json -Depth 10 | Out-File -FilePath "raw_response.json" -Encoding UTF8
    Write-Host "原始响应数据已保存到: raw_response.json" -ForegroundColor Yellow
    
    # 提取加密的题目数据
    $encryptedQuestions = $response.data.questions
    $encryptedKey = $response.data.optional.qEncrypt.key
    
    Write-Host "加密密钥: $encryptedKey" -ForegroundColor Yellow
    Write-Host "加密题目数据长度: $($encryptedQuestions.Length)" -ForegroundColor Yellow
    
    # 保存加密数据到文件以便进一步分析
    $encryptedData = @{
        encryptedKey = $encryptedKey
        encryptedQuestions = $encryptedQuestions
        encryptConfig = $response.data.optional.qEncrypt
    }
    $encryptedData | ConvertTo-Json -Depth 5 | Out-File -FilePath "encrypted_data.json" -Encoding UTF8
    
    Write-Host "加密数据已保存到: encrypted_data.json" -ForegroundColor Yellow
    
    # 分析加密数据格式
    Write-Host "分析加密数据格式..." -ForegroundColor Green
    
    # 解码base64查看前几个字节
    try {
        $keyBytes = [System.Convert]::FromBase64String($encryptedKey)
        $questionsBytes = [System.Convert]::FromBase64String($encryptedQuestions)
        
        Write-Host "密钥前16字节: $([System.BitConverter]::ToString($keyBytes[0..15]))" -ForegroundColor Cyan
        Write-Host "题目数据前16字节: $([System.BitConverter]::ToString($questionsBytes[0..15]))" -ForegroundColor Cyan
        
        # 检查是否是CryptoJS格式
        $saltedPrefix = [System.Text.Encoding]::ASCII.GetBytes("Salted__")
        $keyPrefix = $keyBytes[0..7]
        $questionsPrefix = $questionsBytes[0..7]
        
        $keyIsSalted = $true
        $questionsIsSalted = $true
        
        for ($i = 0; $i -lt 8; $i++) {
            if ($keyPrefix[$i] -ne $saltedPrefix[$i]) {
                $keyIsSalted = $false
            }
            if ($questionsPrefix[$i] -ne $saltedPrefix[$i]) {
                $questionsIsSalted = $false
            }
        }
        
        Write-Host "密钥是CryptoJS格式: $keyIsSalted" -ForegroundColor Cyan
        Write-Host "题目数据是CryptoJS格式: $questionsIsSalted" -ForegroundColor Cyan
        
        # 如果是CryptoJS格式，提取盐
        if ($keyIsSalted) {
            $keySalt = $keyBytes[8..15]
            Write-Host "密钥盐: $([System.BitConverter]::ToString($keySalt))" -ForegroundColor Cyan
        }
        
        if ($questionsIsSalted) {
            $questionsSalt = $questionsBytes[8..15]
            Write-Host "题目数据盐: $([System.BitConverter]::ToString($questionsSalt))" -ForegroundColor Cyan
        }
        
    } catch {
        Write-Host "Base64解码失败: $_" -ForegroundColor Red
    }
    
    Write-Host "数据分析完成，请查看生成的文件进行进一步分析" -ForegroundColor Green
    
} catch {
    Write-Host "获取数据失败: $_" -ForegroundColor Red
}
