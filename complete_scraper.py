#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整题目抓取脚本
解决只获取1道题的问题，获取每个章节的所有题目
"""

import json
import requests
import time
import os
import re
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class CompleteScraper:
    def __init__(self):
        self.base_url = 'https://api.ixunke.cn'
        self.qbank_id = 6
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9',
            'Referer': 'https://servicewechat.com/',
        }
        
        # 确保目录存在
        if not os.path.exists("data"):
            os.makedirs("data")
    
    def extract_token_from_url(self, url):
        """从URL中提取token"""
        if 'token=' in url:
            token_start = url.find('token=') + 6
            token_end = url.find('&', token_start)
            if token_end == -1:
                token_end = len(url)
            return url[token_start:token_end]
        return None
    
    def get_all_questions_for_chapter(self, chapter_id, chapter_name, question_token):
        """获取章节的所有题目（处理分页和多种API策略）"""
        print(f"\n📝 获取章节 '{chapter_name}' 的所有题目...")
        
        all_questions = []
        
        # 策略1: 尝试不同的API路径
        api_paths = [
            '/zhangguangpu/api/v1/question/sequence_practise_nestification',
            '/zhangguangpu/api/question/list',
            '/zhangguangpu/api/question/chapter',
            '/zhangguangpu/api/v1/question/list',
            '/zhangguangpu/api/exam/questions'
        ]
        
        for api_path in api_paths:
            print(f"  尝试API路径: {api_path}")
            questions = self.try_api_path(api_path, chapter_id, question_token)
            
            if questions and len(questions) > len(all_questions):
                all_questions = questions
                print(f"  ✅ 成功获取 {len(questions)} 道题目")
                
                # 如果获取到足够多的题目，就使用这个结果
                if len(questions) > 1:
                    break
            else:
                print(f"  ⚠ 获取到 {len(questions) if questions else 0} 道题目")
        
        # 策略2: 如果还是只有1道题，尝试分页获取
        if len(all_questions) <= 1:
            print("  尝试分页获取...")
            paginated_questions = self.get_paginated_questions(chapter_id, question_token)
            if len(paginated_questions) > len(all_questions):
                all_questions = paginated_questions
        
        # 策略3: 尝试不同的参数组合
        if len(all_questions) <= 1:
            print("  尝试不同参数组合...")
            param_questions = self.try_different_params(chapter_id, question_token)
            if len(param_questions) > len(all_questions):
                all_questions = param_questions
        
        print(f"  📊 最终获取到 {len(all_questions)} 道题目")
        
        if all_questions:
            return {
                "chapter_id": chapter_id,
                "chapter_name": chapter_name,
                "total_questions": len(all_questions),
                "scraped_time": datetime.now().isoformat(),
                "questions": all_questions
            }
        else:
            return None
    
    def try_api_path(self, api_path, chapter_id, question_token):
        """尝试特定的API路径"""
        url = f"{self.base_url}{api_path}"
        
        # 基础参数
        params = {
            'app': 'true',
            'token': question_token,
            'qBankId': self.qbank_id,
            'chapterId': chapter_id,
            'studentAnswer': 1
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, verify=False, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errno' in data and data['errno']:
                    return []
                
                return self.extract_questions_from_response(data)
            else:
                return []
                
        except Exception as e:
            return []
    
    def get_paginated_questions(self, chapter_id, question_token):
        """尝试分页获取题目"""
        all_questions = []
        
        # 尝试不同的分页参数
        page_strategies = [
            {'pageSize': 1000, 'page': 1},
            {'limit': 1000, 'offset': 0},
            {'size': 1000, 'current': 1},
            {'count': 1000, 'start': 0}
        ]
        
        base_url = f"{self.base_url}/zhangguangpu/api/v1/question/sequence_practise_nestification"
        
        for strategy in page_strategies:
            params = {
                'app': 'true',
                'token': question_token,
                'qBankId': self.qbank_id,
                'chapterId': chapter_id,
                'studentAnswer': 1
            }
            params.update(strategy)
            
            try:
                response = requests.get(base_url, params=params, headers=self.headers, verify=False, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'errno' not in data or not data['errno']:
                        questions = self.extract_questions_from_response(data)
                        if len(questions) > len(all_questions):
                            all_questions = questions
                            
                            # 如果这个策略有效且可能有更多页，继续获取
                            if 'page' in strategy and len(questions) >= strategy.get('pageSize', 50):
                                more_questions = self.get_more_pages(base_url, params, question_token)
                                all_questions.extend(more_questions)
                            
                            break
                            
            except Exception as e:
                continue
        
        return all_questions
    
    def get_more_pages(self, base_url, base_params, question_token):
        """获取更多分页数据"""
        more_questions = []
        page = 2
        
        while page <= 10:  # 最多尝试10页
            params = base_params.copy()
            params['page'] = page
            
            try:
                response = requests.get(base_url, params=params, headers=self.headers, verify=False, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'errno' not in data or not data['errno']:
                        questions = self.extract_questions_from_response(data)
                        
                        if questions:
                            more_questions.extend(questions)
                            page += 1
                            time.sleep(0.5)  # 分页请求间隔
                        else:
                            break
                    else:
                        break
                else:
                    break
                    
            except Exception as e:
                break
        
        return more_questions
    
    def try_different_params(self, chapter_id, question_token):
        """尝试不同的参数组合"""
        all_questions = []
        
        base_url = f"{self.base_url}/zhangguangpu/api/v1/question/sequence_practise_nestification"
        
        # 不同的参数组合
        param_combinations = [
            # 移除studentAnswer参数
            {
                'app': 'true',
                'token': question_token,
                'qBankId': self.qbank_id,
                'chapterId': chapter_id
            },
            # 添加type参数
            {
                'app': 'true',
                'token': question_token,
                'qBankId': self.qbank_id,
                'chapterId': chapter_id,
                'studentAnswer': 1,
                'type': 'all'
            },
            # 添加status参数
            {
                'app': 'true',
                'token': question_token,
                'qBankId': self.qbank_id,
                'chapterId': chapter_id,
                'studentAnswer': 1,
                'status': 'all'
            },
            # 添加mode参数
            {
                'app': 'true',
                'token': question_token,
                'qBankId': self.qbank_id,
                'chapterId': chapter_id,
                'studentAnswer': 1,
                'mode': 'practice'
            }
        ]
        
        for params in param_combinations:
            try:
                response = requests.get(base_url, params=params, headers=self.headers, verify=False, timeout=30)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'errno' not in data or not data['errno']:
                        questions = self.extract_questions_from_response(data)
                        if len(questions) > len(all_questions):
                            all_questions = questions
                            
            except Exception as e:
                continue
        
        return all_questions
    
    def extract_questions_from_response(self, data):
        """从响应数据中提取题目"""
        questions = []
        
        if isinstance(data, dict):
            # 尝试不同的数据字段
            possible_fields = ['data', 'result', 'rows', 'questions', 'list', 'items', 'records']
            
            for field in possible_fields:
                if field in data:
                    if isinstance(data[field], list):
                        questions = data[field]
                        break
                    elif isinstance(data[field], dict):
                        # 检查嵌套的列表字段
                        nested_data = data[field]
                        for nested_field in possible_fields:
                            if nested_field in nested_data and isinstance(nested_data[nested_field], list):
                                questions = nested_data[nested_field]
                                break
                        if questions:
                            break
            
            # 如果没有找到列表字段，检查是否直接是题目对象
            if not questions and self.looks_like_question(data):
                questions = [data]
                
        elif isinstance(data, list):
            questions = data
        
        return questions
    
    def looks_like_question(self, obj):
        """判断对象是否像题目数据"""
        if not isinstance(obj, dict):
            return False
        
        question_indicators = ['question', 'title', 'content', 'stem', 'options', 'choices', 'answer', 'questionContent']
        return any(key in obj for key in question_indicators)
    
    def get_chapters(self, chapter_token):
        """获取章节列表"""
        print("📚 获取章节列表...")
        
        url = f"{self.base_url}/zhangguangpu/api/chapter"
        params = {
            'qBankId': self.qbank_id,
            'app': 'true',
            'token': chapter_token
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, verify=False, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'errno' in data and data['errno']:
                    print(f"❌ 获取章节失败: {data.get('errmsg', '未知错误')}")
                    return []
                
                chapters = data.get('data', []) if isinstance(data.get('data'), list) else []
                print(f"✅ 找到 {len(chapters)} 个章节")
                
                return chapters
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return []
    
    def save_data(self, chapter_data):
        """保存数据"""
        if not chapter_data:
            return False
        
        # 清理文件名
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', chapter_data['chapter_name'])
        filename = f"data/{safe_name}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(chapter_data, f, ensure_ascii=False, indent=2)
            print(f"💾 已保存: {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def run_complete_scraping(self):
        """运行完整抓取"""
        print("🎓 光普软考完整题库抓取工具")
        print("=" * 50)
        
        # 获取token
        print("请提供API链接来提取token:")
        
        chapter_url = input("章节列表API URL: ").strip()
        question_url = input("题目列表API URL: ").strip()
        
        if not chapter_url or not question_url:
            print("❌ 请提供完整的API链接")
            return
        
        chapter_token = self.extract_token_from_url(chapter_url)
        question_token = self.extract_token_from_url(question_url)
        
        if not chapter_token or not question_token:
            print("❌ 无法从URL中提取token")
            return
        
        print(f"✅ 章节token: {chapter_token[:20]}...")
        print(f"✅ 题目token: {question_token[:20]}...")
        
        # 获取章节列表
        chapters = self.get_chapters(chapter_token)
        if not chapters:
            return
        
        # 抓取每个章节
        success_count = 0
        total_questions = 0
        
        for i, chapter in enumerate(chapters, 1):
            chapter_id = chapter.get('id') or chapter.get('chapterId')
            chapter_name = chapter.get('name') or chapter.get('title') or f"章节{i}"
            
            print(f"\n[{i}/{len(chapters)}] 处理章节: {chapter_name}")
            
            # 获取章节的所有题目
            chapter_data = self.get_all_questions_for_chapter(chapter_id, chapter_name, question_token)
            
            if chapter_data and self.save_data(chapter_data):
                success_count += 1
                total_questions += chapter_data['total_questions']
            
            # 延时
            if i < len(chapters):
                time.sleep(2)
        
        print(f"\n🎉 抓取完成!")
        print(f"✅ 成功: {success_count}/{len(chapters)} 个章节")
        print(f"📝 总题目: {total_questions} 道")
        print(f"📁 文件保存在: data/ 目录")

def main():
    scraper = CompleteScraper()
    scraper.run_complete_scraping()

if __name__ == "__main__":
    main()
