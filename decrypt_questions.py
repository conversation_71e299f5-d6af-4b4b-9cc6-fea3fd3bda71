#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题库数据解密工具
用于解密小程序API返回的加密题目数据
"""

import json
import base64
import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import hashlib

def decrypt_aes_cbc(encrypted_data, key, iv=None):
    """
    使用AES-CBC模式解密数据
    """
    try:
        # 解码base64
        encrypted_bytes = base64.b64decode(encrypted_data)
        
        # 如果没有提供IV，从加密数据中提取（前16字节）
        if iv is None:
            iv = encrypted_bytes[:16]
            encrypted_bytes = encrypted_bytes[16:]
        
        # 创建解密器
        cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv)
        
        # 解密
        decrypted = cipher.decrypt(encrypted_bytes)
        
        # 去除填充
        decrypted = unpad(decrypted, AES.block_size)
        
        return decrypted.decode('utf-8')
    except Exception as e:
        print(f"解密失败: {e}")
        return None

def decrypt_crypto_js_format(encrypted_data, password):
    """
    解密CryptoJS格式的加密数据
    CryptoJS使用"Salted__"前缀 + 8字节盐 + 加密数据的格式
    """
    try:
        # 解码base64
        encrypted_bytes = base64.b64decode(encrypted_data)
        
        # 检查是否是CryptoJS格式（以"Salted__"开头）
        if encrypted_bytes[:8] != b'Salted__':
            raise ValueError("不是有效的CryptoJS格式")
        
        # 提取盐（8字节）
        salt = encrypted_bytes[8:16]
        
        # 提取加密数据
        ciphertext = encrypted_bytes[16:]
        
        # 使用PBKDF2派生密钥和IV
        key_iv = hashlib.pbkdf2_hmac('md5', password.encode('utf-8'), salt, 1, 48)
        key = key_iv[:32]  # 256位密钥
        iv = key_iv[32:48]  # 128位IV
        
        # 解密
        cipher = AES.new(key, AES.MODE_CBC, iv)
        decrypted = cipher.decrypt(ciphertext)
        
        # 去除PKCS7填充
        padding_length = decrypted[-1]
        decrypted = decrypted[:-padding_length]
        
        return decrypted.decode('utf-8')
    except Exception as e:
        print(f"CryptoJS解密失败: {e}")
        return None

def fetch_and_decrypt_questions():
    """
    获取并解密题目数据
    """
    # API URL
    url = "https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification"
    params = {
        'app': 'true',
        'token': '**************************************************************************************************************************************************************************%3D%3D',
        'qBankId': '4',
        'chapterId': '62',
        'studentAnswer': '1'
    }
    
    print("正在获取数据...")
    response = requests.get(url, params=params)
    
    if response.status_code != 200:
        print(f"请求失败，状态码: {response.status_code}")
        return None
    
    data = response.json()
    print("数据获取成功")
    
    # 提取加密的题目数据
    encrypted_questions = data['data']['questions']
    
    # 提取加密密钥
    encrypt_config = data['data']['optional']['qEncrypt']
    encrypted_key = encrypt_config['key']
    
    print(f"加密密钥: {encrypted_key}")
    print("正在尝试解密密钥...")
    
    # 尝试解密密钥（可能也是加密的）
    # 常见的解密密钥可能是固定的或者基于某种算法
    possible_passwords = [
        'yuyiruankao',
        'ixunke',
        'questions',
        'qbank',
        'default',
        '123456',
        'password'
    ]
    
    decrypted_key = None
    for pwd in possible_passwords:
        try:
            decrypted_key = decrypt_crypto_js_format(encrypted_key, pwd)
            if decrypted_key:
                print(f"密钥解密成功，使用密码: {pwd}")
                print(f"解密后的密钥: {decrypted_key}")
                break
        except:
            continue
    
    if not decrypted_key:
        print("无法解密密钥，尝试直接使用加密密钥...")
        decrypted_key = encrypted_key
    
    print("正在尝试解密题目数据...")
    
    # 尝试解密题目数据
    decrypted_questions = None
    
    # 方法1: 使用解密后的密钥
    if decrypted_key:
        decrypted_questions = decrypt_crypto_js_format(encrypted_questions, decrypted_key)
    
    # 方法2: 如果方法1失败，尝试使用原始密钥
    if not decrypted_questions:
        decrypted_questions = decrypt_crypto_js_format(encrypted_questions, encrypted_key)
    
    # 方法3: 尝试常见密码
    if not decrypted_questions:
        for pwd in possible_passwords:
            decrypted_questions = decrypt_crypto_js_format(encrypted_questions, pwd)
            if decrypted_questions:
                print(f"题目解密成功，使用密码: {pwd}")
                break
    
    if decrypted_questions:
        print("题目数据解密成功！")
        try:
            # 尝试解析为JSON
            questions_data = json.loads(decrypted_questions)
            
            # 保存解密后的数据
            output_file = "decrypted_questions.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(questions_data, f, ensure_ascii=False, indent=2)
            
            print(f"解密后的数据已保存到: {output_file}")
            print(f"题目数量: {len(questions_data) if isinstance(questions_data, list) else '未知'}")
            
            return questions_data
            
        except json.JSONDecodeError as e:
            print(f"解密后的数据不是有效的JSON格式: {e}")
            # 保存为文本文件
            with open("decrypted_questions.txt", 'w', encoding='utf-8') as f:
                f.write(decrypted_questions)
            print("解密后的数据已保存为文本文件: decrypted_questions.txt")
            return decrypted_questions
    else:
        print("题目数据解密失败")
        return None

if __name__ == "__main__":
    result = fetch_and_decrypt_questions()
    if result:
        print("解密完成！")
    else:
        print("解密失败！")
