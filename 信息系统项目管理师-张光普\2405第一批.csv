﻿题目,选项A,选项B,选项C,选项D,答案,答案解析
"【2024年上半年-第1题】 ( )通过财务、客户、内部运营、学习与成长4个角度，将组织战略目标逐层分解转化为细化 指标，有差异化地针对不同的指标进行不同时间周期的绩效评估，有助于组织战略目标的实现。","硬性分布法","尺度评价表法","目标管理法","平衡计分卡法","D","P651, 平衡计分卡法。平衡计分卡法是指通过财务、客户、内部运营、学习与成长4 个角度，将组织的战略目标落实为可操作的衡量指标和目标值，对被评估者进行综合考评的方 法。平衡计分卡法是一种有效的绩效管理工具，可以将组织战略目标逐层分解转化为相互平衡 的细化指标，从而有差异化地针对不同的指标进行不同时间周期的绩效评估，有助于组织战略 目标的实现。这种方法广泛应用于团队和个人的绩效评估。"
"【2024年上半年-第2题】 关于配置审计的描述，不正确的是( )。","配置审计也称配置审核或配置评价，包括功能配置审计和物理配置审计","验证配置项的开发已圆满完成属于功能配置审计","功能配置审计是审计配置项的完整性，物理配置审计是审计配置项的一致性","审计软件即使发现不一致的情况，也不允许自动更新配置库或配置管理数据库，必须由有关 负责人调查后再进行更新","C","P563, 功能配置审计是审计配置项的一致性，物理配置审计是审计配置项的完整性。"
"【2024年上半年-第3题】 关于采购合同的描述，不正确的是( )。","项目经理无需对采购合同进行定期审查，因为合同条款一旦确定就不会发生变化","合同中的违约责任条款是为了在发生违约情况时，双方能够依据合同条款进行纠纷解决","采购合同应明确规定项目的验收标准和方法，以确保交付成果符合项目要求","项目经理应确保采购合同的变更管理遵循组织的变更管理流程，以确保合同变更的合规性和 有效性","A","P498,A 描述是不正确的。项目的建设过程中难免出现一些不可预见的事项，包括要 求修改或变更合同条款的情况，例如，改变系统的功能、开发进度、成本支付及双方各自承担 的责任等。一般在合同订立之后，引起项目范围、合同有关各方权利责任关系变化的事件，均 可以看作是合同变更。"
"【2024年上半年-第4题】 项目组合生命周期由( )4个阶段组成。","启动、规划、执行、收尾","启动、规划、执行、优化","规划、执行、监控、收尾","规划、执行、监控、改进","B","P579, 项目组合生命周期由启动、规划、执行与优化4个阶段组成。"
"【2024年上半年-第5题】 关于数据安全法的描述，不正确的是( )","现行《中华人民共和国数据安全法》于2021年9月1日起正式施行","数据安全法从数据安全与发展、数据安全制度、数据安全保护义务、政务数据安全与开放的 角度对数据安全保护的义务和相应法律责任进行规定","数据安全法是数据安全领域最高位阶的专门法，与网络安全法一起补充了《中华人民共和国 国家安全法》框架下的安全治理法律体系","数据安全法延续了网络安全法生效以来的&quot;一轴两翼三级&quot;的监管体系，通过多方共同参与 实现各地方、各部门对工作集中收集和产生数据的安全管理","D","P719, 数据安全法延续了网络安全法生效以来的&quot;一轴两翼多级&quot;的监管体系，通过 多方共同参与实现各地方、各部门对工作集中收集和产生数据的安全管理。"
"【2024年上半年-第6题】 对于信息系统开发项目来说，其文档一般分为开发文档、产品文档和管理文档。不属于开发文 档 的 是 ( ) 。","可行性研究报告","安全和测试信息","软件集成计划","配置管理计划","D","P569,信息系统开发项目文档分为三类:开发文档、产品文档、管理文档。 +——————-----+——————-----+——————-----+ | &gt; 类型 | &gt; 作用 | &gt; 文档种类 | +——————-----+——————-----+——————-----+ | &gt; 开发文档 | &gt; 描述开发过程本身 | &gt; 可行性研究报告和项目任务书;需求规格说 | | | | 明;功能规格说 | | | | &gt; 明;设计规格说明(包括程序和数据规格说 | | | | 明、开发计划、 | | | | &gt; 软件集成和测试计划、质量保证计划、安全 | | | | 和测试信息等) | +——————-----+——————-----+——————-----+ | &gt; 产品文档 | &gt; 描述开发过程的产物 | &gt; 培训手册;参考手册和用户指南;软件支持 | | | | 手册;产品手册 | | | | &gt; 和信息广告 | +——————-----+——————-----+——————-----+ | &gt; 管理文档 | &gt; 记录项目管理的信息 | &gt; 开发过程的每个阶段的进度和进度变更的记 | | | | 录;软件变更情 | | | | &gt; 况的记录;开发团队的职责定义、项目计划 | | | | 、项目阶段报告; | | | | &gt; | | | | &gt; 配置管理计划 | +——————-----+——————-----+——————-----+"
"【2024年上半年-第7题】 关于项目经理的描述，不正确的是( )。","项目经理应该时刻关注行业的最新发展趋势","项目经理是发起人、团队成员与其他干系人之间的沟通者","项目经理不可以是临时被委任的外部顾问","项目经理领导项目团队实现项目目标","C","P197, 项目经理还需与其他角色紧密协作，如组织经理、专家以及可行性研究分析人 员。在某些情况下，项目经理可以是临时被委任的外部顾问。"
"【2024年上半年-第8题】 项目沟通管理是确保及时、正确地产生、收集、分发、( )和处理项目信息所需的过程。","接 收","存储","分享","分 析","B","P414, 项目沟通管理是确保及时、正确地产生、收集、分发、存储和最终处理项目信 息所需的过程。"
"【2024年上半年-第9题】 在执行质量管理相关过程时，质量经理对提交的成果进行了审查，将发现的问题记录并给出了 纠正措施建议，该质量经理正在执行( )过程。","规划质量管理","持续改进","管理质量 双","控制质量","D","P373, 控制质量是为了评估绩效，确保项目输出完整、正确且满足客户期望，而监督 和记录质量管理活动执行结果的过程。本过程的主要作用:①核实项目可交付成果和工作已经 达到主要干系人的质量要求，可供最终验收;②确定项目输出是否达到预期目的，这些输出需 要满足所有适用标准、要求、法规和规范。"
"【2024年上半年-第10题】 关于信息安全的描述，不正确的是:( )","信息系统安全包括网络安全、操作系统安全、数据库系统安全和应用系统安全","网络安全技术主要包括防火墙、入侵检测与防护、VPN、安全扫描、网络蜜罐技术、用户和 实体行为分析技术","随着物联网、云计算、人工智能、大数据等新一代信息技术的广泛应用，信息安全面临着新 的问题和挑战","信息安全可以划分为设备安全、数据安全、平台安全、行为安全四个层次","D","P47, 针对信息系统，安全可以划分为四个层次:设备安全、数据安全、内容安全、 行为安全。"
"【2024年上半年-第11题】 数字化转型是建立在数字化转换、数字化升级基础上，进一步触及组织核心业务，以新建---种 ( )为目标的高层次转型。","评估模式","组织模式","业务模式","技术模式","C","P656, 数字化转型是建立在数字化转换、数字化升级的基础上，进一步触及组织核心 业务，以新建一种业务模式为目标的高层次转型。"
"【2024年上半年-第12题】 关于定义活动过程的描述，不正确的是( )。","定义活动主要由项目经理主导，也需要其他项目团队成员参与","定义活动输出的里程碑清单是合同要求的强制性的重要时间节点和事件","定义活动完成后，应形成一份详细的活动清单","定义活动过程中必须考虑项目的时间限制和预算约束","B","P303, 里程碑是项目中的重要时点或事件，里程碑清单列出了项目所有的里程碑，并 指明每个里程碑是强制性的(如合同要求的)还是选择性的(如根据历史信息确定的)。里程 碑的持续时间为零，因为它们代表的只是一个重要时间点或事件。"
"【2024年上半年-第13题】 组织在数字化转型中，( )是转型成功与否的关键要素，使组织具备长期竞争优势。","数字人才队伍","组织数字营销","组织数字文化","数字化产品和服务","C","P659, 组织文化是数字化转型成功与否的关键要素，是指导一个团体行为的共同信念、 价值观和思维模式。由于组织文化很难复制，因此它使组织具备长期竞争优势。"
"【2024年上半年-第14题】 有效执行规划绩效域，能够达到的目标不包括( )。","项目以有条理、协调一致的方式推进","对实物资源进行了有效管理","可以根据新出现的和不断变化的需求进行调整","以系统的方法交付项目成果","B","P532, 规划绩效域涉及整个项目期间组织与协调相关的活动与职能，这些活动和职能 是最终交付项目和成果所必须的。在项目整个生命周期过程中，有效执行本绩效域可以实现预 期目标，主要包含:①项目以有条理、协调一致的方式推进;②应用系统的方法交付项目成果; ③对演变情况进行详细说明;④规划投入的时间成本是适当的;⑤规划的内容对管理干系人的 需求而言是充分的;⑥可以根据新出现的和不断变化的需求进行调整。 对实物资源进行了有效管理属于项目工作绩效域。"
"【2024年上半年-第15题】 关于干系人参与计划的描述，不正确的是( )。","干系人参与计划是项目管理计划的组成部分","干系人参与计划基于项目的需要和干系人的期望而制定","干系人参与计划是识别干系人过程的输出","干系人参与计划制订了干系人有效参与和执行项目决策的策略和行动","C","P512, 干系人参与计划是规划干系人参与过程的输出。"
"【2024年上半年-第16题】 How frequently the risks are being reviewed,this should recorded in ( ).","Risk Management Plan","Issue Log","Risk Register","Risk Report","A","风险审查的频率应记录在( )中。 （A.风险管理计划 B.问题日志 C.风险登记 D.风险报告 P467, 风险审计是一种审计类型，可用于评估风险管理过程的有效性。项目经理负责确保按项 目风险管理计划所规定的频率开展风险审计。风险审计可以在日常项目审查会和风险审查会 上开展，团队也可以召开专门的风险审计会。在实施审计前，应明确定义风险审计的程序和目 标。"
"【2024年上半年-第17题】 ( )uses the data and results from the( )process to reflect the overall quality status of the project to the stakeholders.","Control Quality 、Plan Quality Management","Plan Quality 、Management manage Quality","Manage Quality 、Control Quality","Control Quality 、Manage Quality","C","( )使用( )过程中的数据和结果向干系人反映项目的总体质量状况。 （A.控制质量、规划质量管理 （B.规划质量管理、管理质量 （C.管理质量、控制质量 （D.控制质量、管理质量"
"【2024年上半年-第18题】 ( )不属于排列活动顺序过程的工具与技术。","提前量和滞后量","紧前关系绘图法","关键路径法","箭线图法","C","P305.P309, 排列活动顺序过程的工具与技术:1.紧前关系绘图法、2.箭线图法、3. 确定和整合依赖关系、4.提前量和滞后量、5.项目管理信息系统。"
"【2024年上半年-第19题】 在确定项目合同类型时，如果工作性质清楚，但工作量不是很清楚，而且工作不复杂，又需要 快速签订合同。这种情况下建议使用( )。","工料合同","成本补偿合同","总价合同","成本加激励费用合同","A","P478, 工料合同 (Time and Material,T&M)。工料合同(又称时间和手段合同),是 兼具成本补偿合同和总价合同特点的混合型合同。这种合同往往适用于在无法快速编制出准确 的工作说明书的情况下扩充人员、聘用专家或寻求外部支持。"
"【2024年上半年-第20题】 可用于控制进度过程的数据分析技术不包括( )。","迭代燃尽图","偏差分析","趋势分析","备选方案分析","D","P328, 可用作控制进度过程的数据分析技术主要包括:挣值分析、迭代燃尽图、绩效 审查、趋势分析、偏差分析、假设情景分析。"
"【2024年上半年-第21题】 在一个大型信息系统项目中，项目经理发现尽管已经建立了沟通机制，但团队间的沟通依然不 顺畅，项目风险不断上升。项目经理应优先采取的措施是( )。","监督沟通活动执行情况，审查并优化现有沟通机制","增加每周的团队会议次数，强制团队成员交流","对团队成员进行沟通技巧培训，提升沟通质量","引入新的沟通工具，提高信息传递效率","A","在此情况下，优先采取的措施应该是监督沟通活动执行情况，审查并优化现有沟通机 制。因为项目经理需要了解沟通活动是否按照计划执行，确保团队成员之间的沟通得到有效落 实，并审查目前使用的沟通机制，包括沟通工具和流程。监督可以帮助识别问题，并及早采取 纠正措施。审查，可以确定是否存在问题，以及如何改进和优化现有的沟通机制。以更好地满 足团队成员之间的沟通需求。 增加沟通工具或者强制团队成员交流不一定是解决问题的最佳方式。在没有审查和优化现 有沟通机制之前，引入新的沟通工具可能会增加混乱和复杂性。强制团队成员交流也可能会导 致压力和抵触情绪，反而影响沟通质量。 在优化现有沟通机制之后，如果团队成员仍然存在沟通技巧不足的问题，才考虑对团队成 员进行沟通技巧培训，以提升沟通质量。但在当前情况下，优先解决沟通机制上的问题更为重"
"【2024年上半年-第22题】 能够影响&quot;制订项目管理计划&quot;过程的组织过程资产包括( )。 ①组织的标准政策 ②变更控制程序 ③历史项目进度网络图 ④特定行业的项目管理知识体系 ⑤法律法规和安全标准 ⑥历史信息和经验教训知识库","①③④⑥","①②④⑤","①②③⑥","①③⑤⑥","C","P246, 能够影响制订项目管理计划过程的组织过程资产主要包括:组织的标准政策、 流程和程序;项目管理计划模板;变更控制程序，包括修改正式的组织标准、政策、计划、程 序或项目文件，以及批准和确认变更所须遵循的步骤;监督和报告方法、风险控制程序以及沟 通要求;以往类似项目的相关信息(如范围、成本、进度与绩效测量基准、项目日历、项目进 度网络图和风险登记册);历史信息和经验教训知识库等。④⑤属于事业环境因素。"
"【2024年上半年-第23题】 交付绩效域的绩效要点包括( )。 ①质量②成本③风险④可交付物⑤价值的交付","②④⑤","①④⑤","①②③","②③④","B","P540, 绩效要点:1.价值的交付、2.可交付物、3.质量。"
"【2024年上半年-第24题】 虚拟现实的关键技术不包括( )。","动态环境建模技术","人机交互技术","蒙特卡洛分析技术","传感器技术","C","P64, 虚拟现实的关键技术主要涉及人机交互技术、传感器技术、动态环境建模技术 和系统集成技术等。蒙特卡洛分析属于实施定量风险分析的工具。"
"【2024年上半年-第25题】 关于范围管理的描述，正确的是( )。","规划范围管理的作用是在整个项目期间对如何管理范围提供指南和方向","定义范围管理是为了记录如何定义、确认和控制项目范围及产品范围，而创建范围管理计划 的过程","范围管理计划包含配置管理活动和需求优先级排序过程","范围管理计划是非常详细的正式的计划","A","P275, 规划范围管理是为了记录如何定义、确认和控制项目范围及产品范围，而创建 范围管理计划的过程。本过程的主要作用是在整个项目期间对如何管理范围提供指南和方向。 本过程仅开展一次或仅在项目的预定义点开展。B 应该是规划范围管理。 C应该是需求管理计 划。D 范围管理计划可以是正式或非正式的，非常详细或高度概括的。"
"【2024年上半年-第26题】 价值交付系统包括项目如何创造价值、价值交付组件和信息流。其中，价值交付组件包括( )。 ①战略②项目组合③项目集④项目⑤运营 ⑥产品⑦市场环境","②③④⑥⑦","②③④⑤⑥","①②③④⑤","①②③④⑥","B","P221, 可以单独或共同使用多种组件(例如项目组合、项目集、项目、产品和运营) 以创造价值。这些组件共同组成了一个符合组织战略的价值交付系统。"
"【2024年上半年-第27题】 &quot;监督沟通&quot;过程采用的措施不包括( )。","识别和确定沟通需求","审查问题日志、评估变更","整理经验教训、开展团队观察","开展客户满意度调查","A","P427.428, 监督沟通可能需要采取各种方法，例如，开展客户满意度调查、整理经验 教训、开展团队观察、审查问题日志和评估变更。"
"【2024年上半年-第28题】 专利分类中，( )指对产品、方法或者其改进所提出的新的技术方案; ( )指对产品的形状、 构造或者其结合所提出的适于实用的新的技术方案。","实用新型、外观设计","发明、实用新型","发明、外观设计","实用新型、发明","B","P718, 发明创造是指发明、实用新型和外观设计。发明是指对产品、方法或者其改进 所提出的新的技术方案。实用新型是指对产品的形状、构造或者其结合所提出的适于实用的新 的技术方案。外观设计，是指对产品的整体或者局部的形状、图案或者其结合以及色彩与形状、 图案的结合所作出的富有美感并适于工业应用的新设计。"
"【2024年上半年-第29题】 关于成本管理的描述，不正确的是( )。","成本估算和成本预算是两个过程，即使是很小的项目，也需要由不同的人员来分别完成，以 保证估算和预算过程的客观性和独立性","项目成本管理应当考虑项目干系人的需要，不同的项目干系人可能在不同的时间以不同的方 式测算项目的成本","在项目过程中，不应只关心完成项目活动所需资源的成本，也需考虑项目决策对项目最终产 品的使用和维护成本的影响","全生命周期成本管理经常与价值工程技术结合使用，用于降低成本，缩短时间，提高项目可 交付成果的质量和绩效，并优化决策过程","A","P334, 生命期成本管理经常与价值工程技术结合使用，用于降低成本，缩短时间，提 高项目可交付成果的质量和绩效，并优化决策过程。 项目成本管理应当考虑项目干系人的需要，不同的项目干系人可能在不同的时间以不同的 方式测算项目的成本。例如，物品的采购成本可以在做出承诺、发出订单、送达、货物交付时， 实际成本发生时或者为了会计核算的目的记录实际成本时，再进行测算。 就某些项目，特别是小项目而言，成本估算和成本预算之间的关系极其密切，以致可以将 其视为一个过程，由一个人在较短的时间内完成。"
"【2024年上半年-第30题】 由于每个项目都是独特的，项目经理可以根据需要对项目资源管理过程进行裁剪。裁剪时考虑 的 因 素 不 包 括 ( ) 。","团队管理","物理位置","生命周期方法","沟通技术","D","P388, 由于每个项目都是独特的，项目经理可以根据需要对项目资源管理过程进行裁 剪。裁剪时应考虑的因素包括: ● 多 元 化:团队的多元化背景是什么? ● 物理位置:团队成员和实物资源的物理位置在哪里? ● 行业特定资源:所在行业需要哪些特殊资源? ●团队成员的获得:如何获得项目团队成员?项目团队成员是全职还是兼职? ● 团队管理:如何管理项目团队建设?组织是否有管理团队建设的工具或是否需要创建新工 具?是否存在有特殊需求的团队成员?是否需要为团队提供有关多元化管理的特别培训? ●生命周期方法:项目采用哪些生命周期方法?"
"【2024年上半年-第31题】 某公司承接智能会议信息系统项目，有4个方案可供选择。每个方案都可能会出现三种不同的 结果，分别对应获取不同的利润，如下表所示。根据后悔值决策法，该公司应选择方案( )。 +——----+————+————+————+ | | &gt; 最差结果 | &gt; 一般结果 | &gt; 最佳结果 | +——----+————+————+————+ | &gt; 方案甲 | &gt; -5万 | &gt; 15万 | &gt; 30万 | +——----+————+————+————+ | &gt; 方案乙 | &gt; 10万 | &gt; 60万 | &gt; 80万 | +——----+————+————+————+ | &gt; 方案丙 | &gt; 20万 | &gt; 35万 | &gt; 45万 | +——----+————+————+————+ | &gt; 方案丁 | &gt; -60万 | &gt; 10万 | &gt; 50万 | +——----+————+————+————+","甲","乙","丙","丁","B","P636, 本题考查后悔值决策法，可按照如下步骤计算: （1）.计算每个方案在各种情况下的后悔值，先用每列最大值减去本身的数字，形成新的矩阵 （2）.找出各方案的最大后悔值;求得每行的最大值，如下: +——----+————+————+————+——————----+ | | &gt; 最差结果 | &gt; 一般结果 | &gt; 最佳结果 | &gt; 每行最大值(后悔值) | +——----+————+————+————+——————----+ | &gt; 方案甲 | &gt; 25万 | &gt; 45万 | &gt; 50万 | &gt; 50万 | +——----+————+————+————+——————----+ | &gt; 方案乙 | &gt; 10万 | &gt; 0万 | &gt; 0万 | &gt; 10万 | +——----+————+————+————+——————----+ | &gt; 方案丙 | &gt; 0万 | &gt; 25万 | &gt; 35万 | &gt; 35万 | +——----+————+————+————+——————----+ | &gt; 方案丁 | &gt; 80万 | &gt; 50万 | &gt; 30万 | &gt; 80万 | +——----+————+————+————+——————----+ （3）.选择最大后悔值中的最小方案作为最优方案。再从每行最大值这一列中看最小值。是10, 对应乙方案。"
"【2024年上半年-第32题】 关于&quot;管理质量&quot;过程的描述，不正确的是( )。","管理质量是所有人的共同职责，包括项目经理、项目团队、项目发起人、执行组织的管理层","管理质量过程需要在整个项目期间开展","管理质量包括所有质量保证活动，与产品设计和过程改进无关","管理质量是把组织的质量政策用于项目，并将质量管理计划转化为可执行的质量活动的过程","C","P368, 管理质量包括所有质量保证活动，还与产品设计和过程改进有关。"
"【2024年上半年-第33题】 信息系统的运维和服务中，IT 服务管理是通过主动管理和流程的持续改进来确保IT 服务交付 有效且高效的一组活动，( )不属于 IT 服务管理的活动。","配置管理","服务台","事件管理","过程开发","D","P101,IT 服务管理是通过主动管理和流程的持续改进来确保IT 服务交付有效且高效 的 一 组活动。IT 服务管理由若干不同的活动组成:服务台、事件管理、问题管理、变更管理、 配置管理、发布管理、服务级别管理、财务管理、容量管理、服务连续性管理和可用性管理。"
"【2024年上半年-第34题】 项目整合管理过程中，&quot;实施已批准的变更&quot;在( )过程中开展。","实施整体变更控制","指导与管理项目工作","制订项目管理计划","监控项目工作","B","P251, 指导与管理项目工作还要求项目团队回顾所有项目变更的影响，并实施已批准 的变更，包括纠正措施、预防措施和缺陷补救措施。"
"【2024年上半年-第35题】 在一个复杂的信息系统项目中，由于资源限制，项目经理发现关键资源(如资深开发人员)经 常在不同的项目任务之间被重新分配，这导致项目进度频繁延误和质量波动。为了有效控制资 源并减少对进度延迟和成本超支的影响，项目经理优先采取的措施是( )。","实施资源平衡策略，优化不同任务间的资源分配","缩减项目范围以匹配可用资源的能力","增加资源储备以应对未来的资源需求波动","严格限制资源在项目间的共享，确保资源专注于当前任务","A","P319, 资源平衡是为了在资源需求与资源供给之间取得平衡，根据资源制约因素对开 始日期和完成日期进行调整的一种技术。如果共享资源或关键资源只在特定时间可用而且数量 有限，如一个资源在同一时段内被分配至两个或多个活动，就需要进行资源平衡。也可以为保 持资源使用量处于均衡水平而进行资源平衡。"
"【2024年上半年-第36题】 在获取资源过程中， ( )情况下不需要采用预分派。","在完成资源管理计划的前期工作之前，制定项目章程过程或其他过程已经指定了某些团队成 员的工作","项目需要采购外部资源","在竞标过程中承诺分派特定人员进行项目工作","项目取决于特定人员的专有技能","B","P398, 预分派指事先确定项目的实物或团队资源，在如下情况时可采用预分派:①在 竞标过程中承诺分派特定人员进行项目工作;②项目取决于特定人员的专有技能;③在完成资 源管理计划的前期工作之前，制定项目章程过程或其他过程已经指定了某些团队成员的工作。"
"【2024年上半年-第37题】 探索各种选项，权衡包括时间与成本、风险与进度等多种因素，舍弃无效或次优的替代方案， 这种不确定性应对方法是( )。","增加韧性","收集信息","集合设计","为多种结果做好准备","C","P552, 项目中必然存在不确定性，任何活动的影响都无法准确预测，而且可能会产生 一系列的不确定性。针对不确定性的应对方法主要包括: ① 收集信息。可以对信息收集和分析工作进行规划，以便发现更多信息(如进行研究、争取 专家参与或进行市场分析)来减少不确定性。 ② 为多种结果做好准备。制定可用的解决方案，包括备份或应急计划，为每一个不确定性做 好准备。如果存在大量潜在不确定性，项目团队需要对潜在原因进行分类和评估，估算其发生 的可能性。 ③ 集合设计。探索各种选项，来权衡包括时间与成本、质量与成本、风险与进度、进度与质 量等多种因素，在整个过程中，舍弃无效或次优的替代方案，以便项目团队能够从各种备选方 案中选择最佳方案。 ④ 增加韧性。韧性是对意外变化快速适应和应对的能力，韧性既适用于项目团队成员，也适 用于组织过程。如果对产品设计的初始方法或原型无效，则项目团队和组织需要能够快速学习、适应和应对变化。"
"【2024年上半年-第38题】 关于制定预算的描述，不正确的是( )。","制定预算过程的主要作用是确定项目所需资金","成本基准是经过批准且按时间段分配的项目预算","制定预算是汇总所有单个活动或工作包的估算成本，建立一个经批准的成本基准的过程","项目预算包括经批准用于执行项目的全部资金","A","P345, 制定预算主要作用是，确定可以依据其来进行监督和控制项目绩效的成本基准。 估算成本主要作用是确定项目所需的资金。"
"【2024年上半年-第39题】 新型基础设施建设是以新发展理念为引领，以( )为驱动，以信息网络为基础，面向高质量发 展需要的基础设施体系。","工业互联网","技术创新","人工智能","区块链","B","P7, 新型基础设施是以新发展理念为引领，以技术创新为驱动，以信息网络为基础， 面向高质量发展需要，提供数字转型、智能升级、融合创新等服务的基础设施体系。目前， 新型基础设施主要包括如下三个方面:信息基础设施、融合基础设施、创新基础设施。"
"【2024年上半年-第40题】 项目经理根据干系人的需求、期望、利益和对项目的潜在影响，制定项目干系人参与项目的方 法，则该项目经理正在执行( )过程。","监督干系人参与","识别干系人","规划干系人参与","管理干系人参与","C","P504, 项目干系人管理的过程包括: · 识别干系人:定期识别干系人，分析和记录他们的利益、参与度、相互依赖性、影响力和对 项目潜在的影响。 · 规划干系人参与:根据干系人的需求、期望、利益和对项目的潜在影响，制定项目干系人参 与项目的方法。 · 管理干系人参与:与干系人进行沟通和协作，以满足其需求与期望，并处理问题，以促进干 系人合理参与。 · 监督干系人参与:监督项目干系人关系，并通过修订参与策略和计划来引导干系人合理参与 项目"
"【2024年上半年-第41题】 在应用集成中，有多个组件帮助协调连接各种应用。其中( )利用特定的数据结构，帮助开 发人员快速访问其他应用的功能。","API","RNN","数据映射","事件驱动型操作","A","P170, 应用编程接口 (API):API 是定义不同软件交互方式的程序和规则，可以支 持应用之间相互通信。 API 利用特定的数据结构，帮助开发人员快速访问其他应用的功能"
"【2024年上半年-第42题】 某企业管理层采取纠偏措施缩小目标与实际业绩之间的差距。该企业正在执行( )阶段的营销 活动。","市场营销执行","市场营销组织","市场营销分析","市场营销控制","D","P714, 市场营销控制。由于在营销计划的执行过程中会发生许多意想不到的情况，市 场营销者必须进行持续的市场营销控制， 即评价市场营销战略和计划的结果，并采取纠偏措 施以确保既定目标的实现。市场营销控制的步骤包括:①管理层首先要设定具体的营销目标; ②衡量其市场业绩，找到造成预期业绩和实际业绩之间缺口的原因;③管理层采取纠偏措施缩 小目标与实际业绩之间的差距，包括改变行动计划，或者改变目标本身。"
"【2024年上半年-第43题】 流程管理中，组织战略执行保障体系包含战略控制层、流程执行层、系统支撑层三层。其中， ( )是战略执行落地的核心枢纽;预算考核属于( )。","流程执行层 系统支撑层","流程执行层战略控制层","战略控制层系统支撑层","战略控制层流程执行层","B","P681, 在战略明晰的基础上，组织还需要构建战略执行保障体系，具体包括以下三层: ●第一层:以会议管理、运行分析、预算考核为基础建立组织发展计划，形成以执行和控制为 目标的战略控制层。 ●第二层:以业务流程、岗位描述、绩效测评为基础架构，对研发、采购、生产与交付、销售、 客服等各职能领域构建稳定的流程执行层。 ●第三层:以ERP (组织资源规划)、 CRM (客户关系管理)、 PDM (产品数据管理)等大量 的信息技术应用为基础的系统支撑层。 流程执行层是战略执行落地的核心枢纽，在整个战略执行保障体系中起承上启下的作用，组织 的战略目标需要落实到流程上从而方便执行。"
"【2024年上半年-第44题】 IT 审计业务和服务通常分为IT 内部控制审计和IT 专项审计，( )属于IT 内部控制审计。","网络与信息安全审计","信息系统生命周期审计","应用控制审计","数据审计","C","P89,IT 审计业务和服务通常分为IT 内部控制审计和IT 专项审计。IT 内部控制审计 主要包括组织层面IT 控制审计、IT 一般控制审计及应用控制审计;IT 专项审计主要是指根据 当前面临的特殊风险或者需求开展的IT 审计，审计范围为IT 综合审计的某一个或几个部分。"
"【2024年上半年-第45题】 ( )定位于在并发事务中保证数据库中数据的逻辑---致性。","防止非法的数据访问","保证数据的语义完整性","保证数据的操作完整性","保证数据库的完整性","C","P162, 保证数据的操作完整性定位于在并发事务中保证数据库中数据的逻辑一致性。 一般而言，数据库管理系统中的并发管理器子系统负责实现这部分需求。"
"【2024年上半年-第46题】 常用于分析和评估改进机会的两种质量改进工具是PDCA和 ( ) 。","六西格玛","蒙特卡洛模拟","决策树分析","流程图","A","P372,质量改进的开展，可基于质量控制过程的发现和建议、质量审计的发现或管理 质量过程的问题解决。计划---实施---检查一行动和六西格玛是最常用于分析和评估改进机会的 两种质量改进工具。"
"【2024年上半年-第45题】 CMMI 的4大能力域类别中， ( )用于生产和提供优秀解决方案。","提 高(Improving)","实 现(Enabling)","管 理(Managing)","行 动(Doing)","D","P599,CMMI 将所有收集并论证过的最佳实践按逻辑归为4大能力域类别: ● 行 动(Doing): 用于生产和提供优秀解决方案的能力域。 ● 管 理(Managing): 用于策划和管理解决方案实施的能力域。 ● 实 现(Enabling): 用于支持解决方案实施和交付的能力域。 ● 提 高(Improving): 用于维持和提高效率效能的能力域。"
"【2024年上半年-第48题】 监控项目工作过程中，出现偏差时，需使用( )在多个方案中选择要执行的纠正措施和预防措 施;需使用( )技术确定最节约成本的纠正措施;需使用( )技术对范围、进度和成本绩效进 行综合分析。","偏差分析、备选方案分析、成本效益分析","备选方案分析、成本效益分析、挣值分析","偏差分析、备选方案分析、挣值分析","备选方案分析、偏差分析、成本效益分析","B","P260,● 备选方案分析:用于在出现偏差时选择要执行的纠正措施或纠正措施和预防 措施的组合。 ●成本效益分析:有助于出现偏差时确定最节约成本的纠正措施。 ●挣值分析:对范围、进度和成本绩效进行了综合分析。"
"【2024年上半年-第49题】 软件过程能力成熟度模型 (CSMM) 包括治理、开发与交付、管理与支持、( )四个能力域。","运营管理","战略管理","组织管理","数据管理","C","P144, 软件过程能力是组织基于软件过程、技术、资源和人员能力达成业务目标的综 合能力。包括治理能力、开发与交付能力、管理与支持能力、组织管理能力等方面。"
"【2024年上半年-第50题】 国家标准( )为信息技术服务体系的建立提供了范围基础。","《信息技术服务服务基本要求》","《信息技术服务分类与代码》","《信息技术服务服务安全要求》","《信息技术服务从业人员能力评价要求》","B","P728, +————-+————-+————-+————-+————-+ | &gt; 标准编号 | &gt; 标准名称 | &gt; 主要内容 | &gt; 适用范围 | &gt; 类别 | +————-+————-+————-+————-+————-+ | | | &gt; 该标准规定了信 | &gt; 该标准适用于信 | | | | | 息技术服务的分类与代码 | 息技 | | | | | ， | | | +————-+————-+————-+————-+————-+ | &gt; GB/T | &gt; 信息技术服务分 | &gt; 是信息技术服务 | &gt; 术服务的信息管 | &gt; 国家 | | | | 分类、管理和编目的准则 | 理及 | | | | | ， | | | +————-+————-+————-+————-+————-+ | &gt; 29264 | &gt; 类与代码 | &gt; 为信息技术服务 | &gt; 信息交换，供科 | &gt; 标准 | | | | 体系的建立提供了范围基 | 研、 | | | | | | &gt; 规划等工作使用 | | | | | &gt; 础 | | | +————-+————-+————-+————-+————-+"
"【2024年上半年-第51题】 依据ISSE-CMM中公共特性的成熟度等级定义，( )不属于 ISSE-CMM的 Level2: 规划和跟踪级。","将过程域执行的方法形成标准化和程序化文档","验证过程与可用标准的一致性","在执行过程域中，使用文档化的标准和程序","对组织的标准化过程族进行裁剪","D","P179.P180, 通用实施，被称之为公共特性的逻辑域组成，公共特性分为5个级别， 依次表示增强的组织能力。 +——————-----+——————-----+——————-----+ | &gt; 级别 | &gt; 公共特性 | &gt; 通用实施 | +——————-----+——————-----+——————-----+ | &gt; Levell:非正规实 | &gt; 执行基本实施 | &gt; 1.1.1执行过程 | | &gt; 施级 | | | +——————-----+——————-----+——————-----+ | &gt; Level2:规划和跟 | &gt; 规划执行 | &gt; 2.1.1为执行过程分配足够资源 | | &gt; 踪级 | | | | | | &gt; | | | | &gt; 2.1.2为开发工作产品和(或)提供过 | | | | 程域服务指定 | | | | &gt; 责任人 | | | | &gt; | | | | &gt; 2.1.3将过程域执行的方法形成标准化 | | | | 和(或)程序 | | | | &gt; 化文档 | | | | &gt; | | | | &gt; 2.1.4提供支持执行过程域的有关工具 | | | | | | | | &gt; | | | | &gt; 2.1.5保证过程域执行人员获得适当的 | | | | 过程执行方 | | | | &gt; 面的培训 | | | | &gt; | | | | &gt; 2.1.6对过程域的实施进行规划 | +——————-----+——————-----+——————-----+ | | &gt; 规范化执行 | &gt; 2.2.1在执行过程域中，使用文档化的 | | | | 规划、标准 | | | | &gt; 和(或)程序 | | | | &gt; | | | | &gt; 2.2.2在需要的地方将过程域的工作产 | | | | 品置于版本 | | | | &gt; 控制和配置管理之下 | +——————-----+——————-----+——————-----+ | | &gt; 验证执行 | &gt; 2.3.1验证过程与可用标准和(或)程 | | | | 序的一致性 | | | | &gt; | | | | &gt; 2.3.2审计工作产品(验证工作产品遵 | | | | 从可适用标准 | | | | &gt; 和(或)需求的情况) | +——————-----+——————-----+——————-----+ | | &gt; 跟踪执行 | &gt; 2.4.1用测量跟踪过程域相对于规划的 | | | | 态势 | | | | &gt; | | | | &gt; 2.4.2当进程严重偏离规划时采取必要 | | | | 修正措施 | +——————-----+——————-----+——————-----+ | &gt; Level3:充分定义 级 | &gt; 定义标准化过程 | &gt; 3.1.1对过程进行标准化 | | | | | | | | &gt; | | | | &gt; 3.1.2对组织的标准化过程族进行裁剪 | +——————-----+——————-----+——————-----+ | | &gt; 执行已定义的过程 | &gt; 3.2.1在过程域的实施中使用充分定义 | | | | 的过程 | | | | &gt; | | | | &gt; 3.2.2对过程域的适当工作产品进行缺 | | | | 陷评审 | | | | &gt; | | | | &gt; 3.2.3通过使用已定义过程的数据管理 | | | | 该过程 | +——————-----+——————-----+——————-----+ | | &gt; 协调安全实施 | &gt; 3.3.1协调工程科目内部的沟通 | | | | | | | | &gt; | | | | &gt; 3.3.2协调组织内不同组间的沟通 | | | | | | | | &gt; | | | | &gt; 3.3.3协调与外部组间的沟通 | +——————-----+——————-----+——————-----+ | &gt; Level4:量化控制 级 | &gt; 建立可测度的质量 | &gt; 4.1.1为组织标准过程族的工作产品建 | | | &gt; 目标 | 立可测度的 | | | | &gt; 质量目标 | +——————-----+——————-----+——————-----+ | | &gt; 对执行情况实施客 | &gt; 4.2.1量化地确定已定义过程的过程能 | | | &gt; 观管理 | 力 | | | | &gt; | | | | &gt; 4.2.2当过程未按过程能力执行时，适 | | | | 当地采取修正 | | | | &gt; 行动 | +——————-----+——————-----+——————-----+ | &gt; Level5:持续改进 级 | &gt; 改进组织能力 | &gt; 5.1.1为改进过程效能，根据组织的业 | | | | 务目标和当前 | | | | &gt; 过程能力建立量化目标 | | | | | | | | &gt; | | | | &gt; 5.1.2通过改变组织的标准化过程，从 | | | | 而提高过程效 | | | | &gt; 能 | +——————-----+——————-----+——————-----+ | | &gt; 改进过程的效能 | &gt; 5.2.1执行缺陷的因果分析 | | | | | | | | &gt; | | | | &gt; 5.2.2有选择地消除已定义过程中缺陷 | | | | 产生的原因 | | | | &gt; | | | | &gt; 5.2.3通过改变已定义过程来连续地改 | | | | 进实施 | +——————-----+——————-----+——————-----+"
"【2024年上半年-第52题】 监督干系人参与时， ( )用于确定干系人群体和个人在项目任何特定时间的状态。","干系人分析","优先级排序","根本原因分析","职责分配矩阵","A","P516, 适用于监督干系人参与过程的数据分析技术主要包括: ●备选方案分析:在干系人参与效果没有达到期望要求时，应该开展备选方案分析，评估 应对偏差的各种备选方案。 ●根本原因分析:开展根本原因分析，确定干系人参与未达预期效果的根本原因。 ●干系人分析:确定干系人群体和个人在项目任何特定时间的状态。"
"【2024年上半年-第53题】 某项目有四个方案均能满足要求。其费用数据如下表所示。在基准折现率为10%情况下，根据 费用现值法，最优方案是( )。 +————+——————-----+——————————----+ | &gt; 方案 | &gt; 总投资(第1年初) | &gt; 每年运营费用(第1年到第3年) | +————+——————-----+——————————----+ | &gt; 方案甲 | &gt; 150 | &gt; 40 | +————+——————-----+——————————----+ | &gt; 方案乙 | &gt; 300 | &gt; 5 | +————+——————-----+——————————----+ | &gt; 方案丙 | &gt; 200 | &gt; 15 | +————+——————-----+——————————----+ | &gt; 方案丁 | &gt; 250 | &gt; 10 | +————+——————-----+——————————----+","甲","乙","丙","丁","C","P614, 费用现值是不同方案在计算期内的各年成本，按基准收益率换算到基准年的现 值与方案的总投资现值的和。费用现值越小，其方案经济效益越好。 PC甲=150+40/(1+0.1)+40/(1+0.1)²+40/(1+0.1)³=249.47 PCz=300+5/(1+0.1)+5/(1+0.1)²+5/(1+0.1)³=312.43 PC丙=200+15/(1+0.1)+15/(1+0.1)²+15/(1+0.1)³=237.3 PCj=250+10/(1+0.1)+10/(1+0.1)²+10/(1+0.1)³=274.86"
"【2024年上半年-第54题】 在控制采购过程中，项目经理发现某些供应商提供的产品存在质量问题，并且这些问题可能导 致项目延期。项目经理应( )。","通知高层领导，并等待高层领导的决策后，再采取行动","首先与供应商沟通，了解问题的原因，并尝试解决或减轻其对项目的影响","自行决定采取补救措施，如增加项目预算，或延长项目工期，以弥补供应商的问题","立即终止与存在问题的供应商合作，并寻找新的供应商","B","出现问题首先是需要想办法解决问题。"
"【2024年上半年-第55题】 投资100万，年利率10%,按复利法计算，则3年后本利和约为( )万元。","135","133","130","121","B","P609, 复利法按上一期的本利和计息，除本金计息外，利息也生利息，每一计息周期 的利息都要并入下一期的本金，再计利息。复利计算公式为F=P(1+i) +————+————+————-----+——————+ | &gt; 年份 | &gt; 本金 | &gt; 当前利息 | &gt; 本利和 | +————+————+————-----+——————+ | &gt; 1 | &gt; 100万 | &gt; 100*10%=10 | &gt; 100+10=110 | +————+————+————-----+——————+ | &gt; 2 | &gt; 100万 | &gt; 110*10%=11 | &gt; 110=11=121 | +————+————+————-----+——————+ | &gt; 3 | &gt; 100万 | &gt; 121*10%=12.1 | &gt; 121+12.1=133.1 | +————+————+————-----+——————+"
"【2024年上半年-第56题】 在项目执行过程中，项目经理发现项目进展顺利，但某些潜在风险因素仍然存在。为了确保项 目能够顺利完成，项目经理应采取( )措施，有效地监督这些风险。","分析风险因素对项目的潜在影响，并调整项目计划","增加项目预算，以应对可能出现的风险","定期进行风险审查会议，并更新风险登记册","减少项目开支，以降低潜在风险的影响","C","P467, 适用于监督风险过程的会议是风险审查会。应该定期安排风险审查，来检查和 记录风险应对在处理整体项目风险和已识别单个项目风险方面的有效性。在风险审查中，还可 以识别出新的单个项目风险(包括已商定应对措施所引发的次生风险)、重新评估当前风险、 关闭已过时风险、讨论风险发生所引发的问题，以及总结可用于当前项目后续阶段或未来类似 项目的经验教训。根据风险管理计划的规定，风险审查可以是定期项目状态会中的一项议程， 也可以召开专门的风险审查会。"
"【2024年上半年-第57题】 Project Communications Management consists of two parts.The first part is developing a strategy to ensure communication is effective for stakeholders.The second part is carrying out the activities necessary to implement the( ).","communication tools","communication technology","communication","communication cost strategy","D","P414, 项目沟通管理由两部分组成: 一是制定策略，确保沟通对干系人行之有效;二 是执行必要活动以落实( ) A.沟通工具 B.沟通技术 C.沟通成本 D.沟通策略"
"【2024年上半年-第58题】 项目范围说明书包括( )。 ①产品范围描述②需求跟踪矩阵③项目的除外责任④干系人登记册⑤可交付成果 ⑥验收标准","①②③⑤","①③⑤⑥","①②⑤⑥","①②④⑥","B","P284, 项目范围说明书描述要做和不要做的工作的详细程度，决定着项目管理团队控 制整个项目范围的有效程度。详细的项目范围说明书包括内容有(直接列出或参引其他文件): · 产品范围描述:逐步细化在项目章程和需求文件中所述的产品、服务或成果特征。 · 可交付成果:为完成某一过程、阶段或项目而必须产出的任何独特并可核实的产品、成果 或服务能力，可交付成果也包括各种辅助成果，如项目管理报告和文件。对可交付成果的描 述可略可详。 · 验收标准:可交付成果通过验收前必须满足的一系列条件。 · 项目的除外责任:识别排除在项目之外的内容。明确说明哪些内容不属于项目范围，有助于 管理干系人的期望及减少范围蔓延。"
"【2024年上半年-第59题】 在一个大型项目实施过程中，项目经理发现项目风险已经发生，并且对项目进度和成本产生了 显著影响。为了准确评估风险对项目目标的影响程度，项目经理应该采取的定量风险分析技术 是 ( ) 。","风险分类","风险概率和影响评估","敏感性分析","风险数据质量评估","C","P455, 定量风险分析技术包括:模拟、敏感性分析、决策树分析、影响图。"
"【2024年上半年-第60题】 成本管理计划中通常不会规定( )。","控制临界值","精确度","准确度","薪酬结构","D","P340, 在成本管理计划中一般需要规定:计量单位、精确度、准确度、组织程序链接、 控制临界值、绩效测量规则、报告形式、其他细节。"
"【2024年上半年-第61题】 The project manager should use( )tool for the purpose to report on the work remaining for projects.","cumulative flow diagram","burn down chart","forecast estimation","earned value","B","项目管理应该使用( )工具用于追踪迭代未完项中尚待完成的工作。 A.累计流图 B.燃尽图 C.预测估计 D.挣值分析"
"【2024年上半年-第62题】 项目评估指在( )的基础上，由( )根据国家颁布的政策、法规、方法、参数和条例等条件下， 进行评价、分析和论证。","项目立项申请、第三方","项目立项申请、项目承建方","项目可行性研究、第三方","项目可行性研究、项目建设方","C","P234, 项目评估指在项目可行性研究的基础上，由第三方(国家、银行或有关机构) 根据国家颁布的政策、法规、方法、参数和条例等，从国民经济与社会、组织业务等角度出发， 对拟建项目建设的必要性、建设条件、生产条件、市场需求、工程技术、经济效益和社会效益 等进行评价、分析和论证，进而判断其是否可行的一个评估过程。"
"【2024年上半年-第63题】 ( )包括收到的变更请求的数量，接受的变更请求的数量或者确认和完成的可交付成果的数量; 控制范围过程产生的( )包括收到的变更的分类、识别的范围偏差和原因、偏差对进度和成本 的影响，以及对将来范围绩效的预测。","工作绩效数据、工作绩效信息","工作绩效数据、工作绩效报告","工作绩效报告、工作绩效信息","工作绩效信息、工作绩效报告","A","P294, 工作绩效数据:工作绩效数据可能包括收到的变更请求的数量，接受的变更请 求的数量或者核实、确认和完成的可交付成果的数量。 工作绩效信息:控制范围过程产生的工作绩效信息是有关项目和产品范围实施情况(对照范围 基准)的相互关联且与各种背景相结合的信息，包括收到的变更的分类、识别的范围偏差和原 因、偏差对进度和成本的影响，以及对将来范围绩效的预测。"
"【2024年上半年-第64题】 ( )is the process of specifying the approach and identifying potential sellers.","Plan Procurement Management","Close Procurement Management","Control Procurement","Conduct Procurement","A","( )是明确采购方法和识别潜在卖方的过程。 A.规划采购管理 B.关闭采购管理 C.控制采购 D.实施采购"
"【2024年上半年-第65题】 IT 审计常用的审计技术不包括( )。","审计抽样技术","大数据审计技术","产品分析技术","风险评估技术","C","P84, 常 用 的IT 审计技术包括风险评估技术、审计抽样技术、计算机辅助审计技术及 大数据审计技术。"
"【2024年上半年-第66题】 两家企业生产同种产品，为抢占市场规模，每家企业都制定了三个市场推销策略。两家企业的 市场策略会影响双方的市场规模占有情况，赢得矩阵如下: +————---+————---+————---+————---+ | | &gt; 乙企业策略1 | &gt; 乙企业策略2 | &gt; 乙企业策略3 | +————---+————---+————---+————---+ | &gt; 甲企业策略1 | &gt; -2 | &gt; -1 | &gt; 3 | +————---+————---+————---+————---+ | &gt; 甲企业策略2 | &gt; 1 | &gt; 0 | &gt; 1 | +————---+————---+————---+————---+ | &gt; 甲企业策略3 | &gt; 3 | &gt; -1 | &gt; -3 | +————---+————---+————---+————---+ 在这个矩阵中，每个单元格中的数字表示甲企业比乙企业多占的市场规模，负数则表示甲企业 比乙企业少占的市场规模。则双方各应采用哪种策略( )。","甲企业策略2,乙企业策略2","甲企业策略1,乙企业策略2","甲企业策略1,乙企业策略3","甲企业策略3,乙企业策略1","A","考查博弈论，第一步，选出每列最大值，选出每列最小值。第二步，选出&quot;列最大值&quot; 中的最小值，选出&quot;行最小值&quot;中的最大值，对应的策略即为答案。 +————---+————---+————---+————---+——-+ | | &gt; 乙企业策略1 | &gt; 乙企业策略2 | &gt; 乙企业策略3 | &gt; Min | +————---+————---+————---+————---+——-+ | &gt; 甲企业策略1 | &gt; -2 | &gt; -1 | &gt; 3 | &gt; -2 | +————---+————---+————---+————---+——-+ | &gt; 甲企业策略2 | &gt; 1 | &gt; 0 | &gt; 1 | &gt; 0 | +————---+————---+————---+————---+——-+ | &gt; 甲企业策略3 | &gt; 3 | &gt; -1 | &gt; -3 | &gt; -3 | +————---+————---+————---+————---+——-+ | &gt; Max | &gt; 3 | &gt; 0 | &gt; 3 | | +————---+————---+————---+————---+——-+"
"【2024年上半年-第67题】 两化融合是信息化和工业化的高层次的深度结合，主要在技术、产品、( )、产业四个方面进 行融合。","硬 件","软件","业务","网 络","C","P13, 信息化与工业化主要在技术、产品、业务、产业四个方面进行融合。"
"【2024年上半年-第68题】 IT治理活动的主要任务聚焦在全局统筹，价值导向，机制保障，创新发展，文化助推五个方面。 其中&quot;指导建立规范过程管理和痕迹管理，并向利益相关者公开质量设定举措&quot;属于( )的内","全局统筹","机制保障","创新发展","价值导向","B","P74, 机制保障是指组织应对自身IT 发展进行有效管控，保证IT 需求与实现的协调 发展，并使IT 安全和风险得到有效的识别、管理、防范和处置。组织需要建立适合组织特点 的机制保障方法，满足疏漏互补、协同发展、监督改进和安全风险可控的原则，避免扭曲决策 目标方向。组织需要明确管理责任，明晰上下左右权利关系，落实责任制和各项措施。组织可 以根据相关法律法规、行业管理和上级监管机构发布的规范文件要求，制定本组织的信息技术 治理制度并实施，重点聚焦在:①指导建立规范过程管理和痕迹管理，并向利益相关者公开质 量设定举措;②评审IT 管理体系的适宜性、充分性和有效性;③审计IT 完整性、有效性和合 规性;④监督由审计和管理评审，提出改进内容的实施。"
"【2024年上半年-第69题】 信息安全管理的CIA三要素指的是( )。","保密性、完整性、可用性","---致性、可用性、完整性","保密性、有效性、可用性","---致性、可用性、有效性","A","P125,CIA 三要素是保密性 (Confidentiality) 、 完整性(Integrity) 和可用性 (Availability) 三个词的缩写。"
"【2024年上半年-第70题】 组织通用治理中，组织在分析和回顾战略实施过程中进行创新和改进的要素包括( )。 ①内外部发展环境对战略规划的影响 ②在业务增长、发展趋势等方面的预测及其与实际的差异 ③提升业务增长和盈利的措施 ④竞争优势和发展水平分析及措施 ⑤流程规划和知识管理","①②③④","①③④⑤","②③④⑤","①②④⑤","A","P645, 在分析和回顾战略实施过程中进行创新和改进的要素主要包括: · 内外部发展环境对战略规划的影响，包括客户和用户需求、技术或监管环境等; · 在业务增长、发展趋势等方面的预测及其与实际的差异; · 提升业务增长和盈利的措施; · 竞争优势和发展水平分析及措施;· 风险分析及措施。"
"【2024年上半年-第71题】 关于可行性研究的描述，正确的是( )。","初步可行性研究与详细可行性研究在占有资源和研究细节方面是相同的","辅助研究用于解决项目的核心问题，为判断是否具备必要的技术、实验、人力条件提供支持","试验室和中间工厂的试验是初步可行性研究的主要内容","详细可行性研究一般是在对市场或者客户情况进行调查后，对项目进行的初步评估","B","P228.229, 初步可行性研究一般是在对市场或者客户情况进行调查后，对项目进行的 初步评估。所以D 错。 进行初步可行性评估，可以从如下方面进行衡量，以便决定是否开始详细可行性研究: ●分析项目的前途，从而决定是否应该继续深入调查研究; ●初步估计和确定项目中的关键技术及核心问题，以确定是否需要解决; ●初步估计必须进行的辅助研究，以解决项目的核心问题，并判断是否具备必要的技术、 实验、人力条件作为支持等。 初步可行性研究的结果及研究的主要内容基本与详细可行性研究相同。所不同的是占有的 资源、研究细节方面有较大差异。所以A错。 初步可行性研究的主要内容包括:所以B 错。 ●需求与市场预测:包括客户和服务对象需求分析预测，营销和推广分析，如初步的销售 量和销售价格预测。 ●设备与资源投入分析:包括从需求、设计、开发、安装实施到运营的所有设备与材料的 投入分析。 ●空间布局:如网络规划、物理布局方案的选择。 ●项目设计:包括项目总体规划、信息系统设计和设备计划、网络工程规划等。 ●项目进度安排:包括项目整体周期、里程碑阶段划分等。 ●项目投资与成本估算:包括投资估算、成本估算、资金渠道及初步筹集方案等。"
"【2024年上半年-第72题】 关于规划风险管理过程的描述，不正确的是( )。","规划风险管理是定义如何实施项目风险管理活动的过程","规划风险管理在项目实施阶段开始，并在项目执行期间持续进行","干系人分析法可用于规划风险管理过程","规划风险管理的主要作用是确保风险管理的水平、方法和可见度与项目风险程度相匹配","B","P439, 规划风险管理过程在项目立项阶段就应开始，并在项目早期完成。"
"【2024年上半年-第73题】 某项目有4个硬件的生产任务需要完成，有4个硬件厂商可选择，每个厂商只能分配一个任务。 下表是各厂商完成各硬件生产所需的时间: +——----+————-+————-+————-+————-+ | | &gt; 硬件1 | &gt; 硬件2 | &gt; 硬件3 | &gt; 硬件4 | +——----+————-+————-+————-+————-+ | &gt; 厂商甲 | &gt; 13 | &gt; 6 | &gt; 8 | &gt; 9 | +——----+————-+————-+————-+————-+ | &gt; 厂商乙 | &gt; 6 | &gt; 11 | &gt; 5 | &gt; 8 | +——----+————-+————-+————-+————-+ | &gt; 厂商丙 | &gt; 7 | &gt; 9 | &gt; 5 | &gt; 8 | +——----+————-+————-+————-+————-+ | &gt; 厂商丁 | &gt; 8 | &gt; 6 | &gt; 7 | &gt; 12 | +——----+————-+————-+————-+————-+ 则4个硬件生产完成的总时间最短为( )。","25","26","27","28","B","匈牙利算法。 (1)首先找出每行的最小值，然后该行每个数值都减去这个数，得到一个矩阵。(行变换) +——----+————-+————-+————-+————-+ | | &gt; 硬件1 | &gt; 硬件2 | &gt; 硬件3 | &gt; 硬件4 | +——----+————-+————-+————-+————-+ | &gt; 厂商甲 | &gt; 7 | &gt; 0 | &gt; 2 | &gt; 3 | +——----+————-+————-+————-+————-+ | &gt; 厂商乙 | &gt; 1 | &gt; 6 | &gt; 0 | &gt; 3 | +——----+————-+————-+————-+————-+ | &gt; 厂商丙 | &gt; 2 | &gt; 4 | &gt; 0 | &gt; 3 | +——----+————-+————-+————-+————-+ | &gt; 厂商丁 | &gt; 2 | &gt; 0 | &gt; 1 | &gt; 6 | +——----+————-+————-+————-+————-+ (2)在上面的矩阵中，再找出每列的最小值，然后该列每个数值都减去这个数，又可以得到一 个矩阵。(列变换) +——----+————-+————-+————-+————-+ | | &gt; 硬件1 | &gt; 硬件2 | &gt; 硬件3 | &gt; 硬件4 | +——----+————-+————-+————-+————-+ | &gt; 厂商甲 | &gt; 6 | &gt; 0 | &gt; 2 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商乙 | &gt; 0 | &gt; 6 | &gt; 0 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商丙 | &gt; 1 | &gt; 4 | &gt; 0 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商丁 | &gt; 1 | &gt; 0 | &gt; 1 | &gt; 3 | +——----+————-+————-+————-+————-+ (3)在第2步所得的矩阵中，0即为可以安排的对应工作。 (找独立0) +——----+————-+————-+————-+————-+ | | &gt; 硬件1 | &gt; 硬件2 | &gt; 硬件3 | &gt; 硬件4 | +——----+————-+————-+————-+————-+ | &gt; 厂商甲 | &gt; 6 | &gt; 0 | &gt; 2 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商乙 | &gt; 0 | &gt; 6 | &gt; 0 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商丙 | &gt; 1 | &gt; 4 | &gt; 0 | &gt; 0 | +——----+————-+————-+————-+————-+ | &gt; 厂商丁 | &gt; 1 | &gt; 0 | &gt; l | &gt; 3 | +——----+————-+————-+————-+————-+ (4)硬件1所在的列有独立的0元素，所以硬件1由厂商乙生产，排除厂商乙之后，硬件3所 在的列也出现独立的0元素，所以硬件3由厂商丙生产。排除厂商乙和丙之后，硬件4所在的 列也出现独立的0元素，所以硬件4由厂商甲生产，硬件2由厂商丁生产。最终生产方案为: 厂商甲生产硬件4,需要时间9;厂商乙生产硬件1,需要时间6;厂商丙生产硬件3,需要时 间5;厂商丁生产硬件2,需要时间6。最短时间=9+6+5+6=26。"
"【2024年上半年-第74题】 OPM框架的关键要素包括( )。","OPM战略、OPM方法论、风险管理和人才管理","0PM 治理、OPM 方法论、知识管理和风险管理","OPM 战略、0PM方法论、知识管理和人才管理","0PM治理、OPM方法论、知识管理和人才管理","D","P584,OPM 框架的关键要素包括: OPM治理、OPM方法论、知识管理和人才管理。"
"【2024年上半年-第75题】 ( )主要关注大数据存储、大数据协同和安全隐私等方面。","大数据管理技术","大数据处理技术","大数据可视化技术","大数据获取技术","A","P56, 大数据管理技术主要集中在大数据存储、大数据协同和安全隐私等方面。"
