<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量JSON转CSV工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #4facfe;
            background-color: #f0f8ff;
        }
        
        .upload-area.dragover {
            border-color: #4facfe;
            background-color: #e3f2fd;
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .file-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4facfe;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
        }
        
        .file-size {
            font-size: 0.9em;
            color: #666;
        }
        
        .file-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-waiting {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-processing {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .progress-container {
            margin: 20px 0;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
            color: #666;
        }
        
        .results-area {
            margin-top: 30px;
            display: none;
        }
        
        .summary-card {
            background: #e8f5e8;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .summary-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #155724;
            margin-bottom: 10px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #4facfe;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .download-all-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            font-size: 1.1em;
            padding: 15px 30px;
            margin: 20px 0;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 批量JSON转CSV工具</h1>
            <p>支持同时上传多个JSON文件，批量转换为CSV格式</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <ul>
                    <li><strong>批量上传：</strong>支持同时选择多个JSON文件进行批量处理</li>
                    <li><strong>输出格式：</strong>题目、选项A、选项B、选项C、选项D、答案、答案解析</li>
                    <li><strong>自动处理：</strong>自动清理HTML标签，格式化选项和答案</li>
                    <li><strong>批量下载：</strong>可以单独下载每个文件，也可以打包下载所有文件</li>
                </ul>
            </div>
            
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">拖拽多个JSON文件到此处，或点击选择文件</div>
                <button class="btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
                <input type="file" id="fileInput" class="file-input" accept=".json" multiple>
            </div>
            
            <div class="file-list" id="fileList">
                <h3>📋 文件列表</h3>
                <div id="fileItems"></div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" id="processBtn" onclick="processAllFiles()">开始批量转换</button>
                    <button class="btn btn-danger" onclick="clearAllFiles()">清空列表</button>
                </div>
            </div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>
            
            <div class="results-area" id="resultsArea">
                <div class="summary-card">
                    <div class="summary-title">📊 转换结果统计</div>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="totalFiles">0</div>
                            <div class="stat-label">总文件数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="successFiles">0</div>
                            <div class="stat-label">成功转换</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="totalQuestions">0</div>
                            <div class="stat-label">总题目数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="failedFiles">0</div>
                            <div class="stat-label">转换失败</div>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <button class="btn download-all-btn" id="downloadAllBtn" onclick="downloadAllCSV()">
                            📦 打包下载所有CSV文件
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let processedResults = [];
        let isProcessing = false;
        
        // 文件上传区域事件
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            const jsonFiles = Array.from(files).filter(file => file.name.endsWith('.json'));
            
            if (jsonFiles.length === 0) {
                alert('请选择JSON格式的文件');
                return;
            }
            
            // 添加到文件列表（避免重复）
            jsonFiles.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });
            
            displayFileList();
        }
        
        function displayFileList() {
            const fileList = document.getElementById('fileList');
            const fileItems = document.getElementById('fileItems');
            
            if (selectedFiles.length === 0) {
                fileList.style.display = 'none';
                return;
            }
            
            fileItems.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const item = document.createElement('div');
                item.className = 'file-item';
                item.innerHTML = `
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                    <div class="file-status status-waiting" id="status-${index}">等待处理</div>
                    <button class="btn" onclick="removeFile(${index})" style="margin-left: 10px; padding: 5px 10px;">移除</button>
                `;
                fileItems.appendChild(item);
            });
            
            fileList.style.display = 'block';
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displayFileList();
        }
        
        function clearAllFiles() {
            selectedFiles = [];
            processedResults = [];
            displayFileList();
            document.getElementById('resultsArea').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'none';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        async function processAllFiles() {
            if (selectedFiles.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            if (isProcessing) return;
            
            isProcessing = true;
            processedResults = [];
            
            const processBtn = document.getElementById('processBtn');
            processBtn.disabled = true;
            processBtn.textContent = '处理中...';
            
            const progressContainer = document.getElementById('progressContainer');
            progressContainer.style.display = 'block';
            
            let successCount = 0;
            let totalQuestions = 0;
            
            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                const statusElement = document.getElementById(`status-${i}`);
                
                // 更新状态
                statusElement.className = 'file-status status-processing';
                statusElement.textContent = '处理中';
                
                // 更新进度
                const progress = (i / selectedFiles.length) * 100;
                updateProgress(progress, `正在处理: ${file.name} (${i + 1}/${selectedFiles.length})`);
                
                try {
                    const result = await processFile(file);
                    
                    if (result.success) {
                        statusElement.className = 'file-status status-success';
                        statusElement.textContent = `成功 (${result.questionCount}题)`;
                        successCount++;
                        totalQuestions += result.questionCount;
                        processedResults.push(result);
                    } else {
                        statusElement.className = 'file-status status-error';
                        statusElement.textContent = '失败';
                    }
                } catch (error) {
                    statusElement.className = 'file-status status-error';
                    statusElement.textContent = '错误';
                    console.error(`处理文件 ${file.name} 时出错:`, error);
                }
                
                // 添加延迟避免界面卡顿
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // 完成处理
            updateProgress(100, '批量处理完成');
            showResults(selectedFiles.length, successCount, totalQuestions);
            
            processBtn.disabled = false;
            processBtn.textContent = '开始批量转换';
            isProcessing = false;
        }
        
        function processFile(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    try {
                        const jsonData = JSON.parse(e.target.result);
                        const questions = extractQuestions(jsonData);
                        
                        if (questions.length === 0) {
                            resolve({ success: false, error: '未找到题目数据' });
                            return;
                        }
                        
                        const csvData = convertToCSVFormat(questions);
                        
                        resolve({
                            success: true,
                            fileName: file.name,
                            csvData: csvData,
                            questionCount: questions.length
                        });
                        
                    } catch (error) {
                        resolve({ success: false, error: error.message });
                    }
                };
                
                reader.onerror = function() {
                    resolve({ success: false, error: '文件读取失败' });
                };
                
                reader.readAsText(file, 'utf-8');
            });
        }
        
        function extractQuestions(jsonData) {
            let questions = [];
            
            if (jsonData.data && jsonData.data.questions) {
                questions = jsonData.data.questions;
            } else if (jsonData.data && Array.isArray(jsonData.data)) {
                questions = jsonData.data;
            } else if (jsonData.questions) {
                questions = jsonData.questions;
            } else if (jsonData.list) {
                questions = jsonData.list;
            } else if (Array.isArray(jsonData)) {
                questions = jsonData;
            } else if (jsonData.stem || jsonData.content) {
                questions = [jsonData];
            }
            
            return questions;
        }
        
        function convertToCSVFormat(questions) {
            const headers = ['题目', '选项A', '选项B', '选项C', '选项D', '答案', '答案解析'];
            let csv = headers.join(',') + '\n';
            
            questions.forEach(question => {
                const row = processQuestion(question);
                const csvRow = headers.map(header => {
                    const value = row[header] || '';
                    return '"' + value.replace(/"/g, '""') + '"';
                }).join(',');
                csv += csvRow + '\n';
            });
            
            return csv;
        }
        
        function processQuestion(question) {
            const stem = cleanHTML(question.stem || question.content || '');
            const options = question.options || [];
            const separatedOptions = separateOptions(options);
            const answer = question.answer || '';
            const formattedAnswer = formatAnswer(answer);
            const analysis = cleanHTML(question.analysis || question.explanation || '');
            
            return {
                '题目': stem,
                '选项A': separatedOptions.A,
                '选项B': separatedOptions.B,
                '选项C': separatedOptions.C,
                '选项D': separatedOptions.D,
                '答案': formattedAnswer,
                '答案解析': analysis
            };
        }
        
        function cleanHTML(text) {
            if (!text) return '';
            return text.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
        }
        
        function separateOptions(options) {
            const result = { A: '', B: '', C: '', D: '' };
            if (!options || options.length === 0) return result;
            
            const labels = ['A', 'B', 'C', 'D'];
            options.forEach((option, index) => {
                if (index < labels.length) {
                    result[labels[index]] = cleanHTML(option);
                }
            });
            
            return result;
        }
        
        function formatAnswer(answer) {
            if (!answer) return '';
            
            const labels = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            if (Array.isArray(answer)) {
                return answer.map(idx => 
                    (typeof idx === 'number' && idx >= 0 && idx < labels.length) ? labels[idx] : idx
                ).join(', ');
            }
            
            if (typeof answer === 'number' && answer >= 0 && answer < labels.length) {
                return labels[answer];
            }
            
            return cleanHTML(String(answer));
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        function showResults(total, success, totalQuestions) {
            document.getElementById('totalFiles').textContent = total;
            document.getElementById('successFiles').textContent = success;
            document.getElementById('totalQuestions').textContent = totalQuestions;
            document.getElementById('failedFiles').textContent = total - success;
            
            document.getElementById('resultsArea').style.display = 'block';
        }
        
        function downloadAllCSV() {
            if (processedResults.length === 0) {
                alert('没有可下载的文件');
                return;
            }
            
            // 如果只有一个文件，直接下载
            if (processedResults.length === 1) {
                downloadSingleCSV(processedResults[0]);
                return;
            }
            
            // 多个文件时，逐个下载
            processedResults.forEach((result, index) => {
                setTimeout(() => {
                    downloadSingleCSV(result);
                }, index * 500); // 间隔500ms下载，避免浏览器阻止
            });
        }
        
        function downloadSingleCSV(result) {
            try {
                const csvContent = '\ufeff' + result.csvData;
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const fileName = result.fileName.replace('.json', '.csv');
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                
                a.href = url;
                a.download = fileName;
                a.style.display = 'none';
                
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
            } catch (error) {
                console.error('下载失败:', error);
            }
        }
    </script>
</body>
</html>
