# PowerShell脚本用于解密题库数据
# 使用.NET Framework的加密功能

Add-Type -AssemblyName System.Security
Add-Type -AssemblyName System.Web

function Convert-FromBase64 {
    param([string]$Base64String)
    try {
        return [System.Convert]::FromBase64String($Base64String)
    }
    catch {
        Write-Host "Base64解码失败: $_" -ForegroundColor Red
        return $null
    }
}

function Get-MD5Hash {
    param([byte[]]$InputBytes)
    $md5 = [System.Security.Cryptography.MD5]::Create()
    return $md5.ComputeHash($InputBytes)
}

function Decrypt-CryptoJS {
    param(
        [string]$EncryptedData,
        [string]$Password
    )
    
    try {
        Write-Host "尝试使用密码解密: $Password"
        
        # 解码Base64
        $encryptedBytes = Convert-FromBase64 -Base64String $EncryptedData
        if ($null -eq $encryptedBytes) {
            return $null
        }
        
        # 检查是否是CryptoJS格式（以"Salted__"开头）
        $saltedPrefix = [System.Text.Encoding]::ASCII.GetBytes("Salted__")
        $prefix = $encryptedBytes[0..7]
        
        $isSalted = $true
        for ($i = 0; $i -lt 8; $i++) {
            if ($prefix[$i] -ne $saltedPrefix[$i]) {
                $isSalted = $false
                break
            }
        }
        
        if (-not $isSalted) {
            Write-Host "不是有效的CryptoJS格式"
            return $null
        }
        
        # 提取盐（8字节）
        $salt = $encryptedBytes[8..15]
        
        # 提取加密数据
        $ciphertext = $encryptedBytes[16..($encryptedBytes.Length - 1)]
        
        # 使用EVP_BytesToKey算法派生密钥和IV
        $passwordBytes = [System.Text.Encoding]::UTF8.GetBytes($Password)
        $keyIv = Get-EVPBytesToKey -Password $passwordBytes -Salt $salt -KeyLen 32 -IvLen 16
        
        # 创建AES解密器
        $aes = [System.Security.Cryptography.Aes]::Create()
        $aes.Mode = [System.Security.Cryptography.CipherMode]::CBC
        $aes.Padding = [System.Security.Cryptography.PaddingMode]::PKCS7
        $aes.Key = $keyIv.Key
        $aes.IV = $keyIv.IV
        
        # 解密
        $decryptor = $aes.CreateDecryptor()
        $decryptedBytes = $decryptor.TransformFinalBlock($ciphertext, 0, $ciphertext.Length)
        
        # 转换为字符串
        $decryptedText = [System.Text.Encoding]::UTF8.GetString($decryptedBytes)
        
        $aes.Dispose()
        $decryptor.Dispose()
        
        return $decryptedText
    }
    catch {
        Write-Host "解密失败: $_" -ForegroundColor Red
        return $null
    }
}

function Get-EVPBytesToKey {
    param(
        [byte[]]$Password,
        [byte[]]$Salt,
        [int]$KeyLen,
        [int]$IvLen
    )
    
    $targetKeySize = $KeyLen + $IvLen
    $derivedBytes = New-Object byte[] $targetKeySize
    $numberOfDerivedWords = 0
    $block = $null
    
    while ($numberOfDerivedWords -lt $targetKeySize) {
        $md5 = [System.Security.Cryptography.MD5]::Create()
        
        if ($null -ne $block) {
            $md5.TransformBlock($block, 0, $block.Length, $null, 0) | Out-Null
        }
        
        $md5.TransformBlock($Password, 0, $Password.Length, $null, 0) | Out-Null
        $md5.TransformFinalBlock($Salt, 0, $Salt.Length) | Out-Null
        
        $block = $md5.Hash
        
        $copyLength = [Math]::Min($block.Length, $targetKeySize - $numberOfDerivedWords)
        [Array]::Copy($block, 0, $derivedBytes, $numberOfDerivedWords, $copyLength)
        $numberOfDerivedWords += $copyLength
        
        $md5.Dispose()
    }
    
    $key = New-Object byte[] $KeyLen
    $iv = New-Object byte[] $IvLen
    
    [Array]::Copy($derivedBytes, 0, $key, 0, $KeyLen)
    [Array]::Copy($derivedBytes, $KeyLen, $iv, 0, $IvLen)
    
    return @{
        Key = $key
        IV = $iv
    }
}

function Get-QuestionsData {
    $url = "https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification?app=true&token=**************************************************************************************************************************************************************************%3D%3D&qBankId=4&chapterId=62&studentAnswer=1"

    Write-Host "正在获取数据..." -ForegroundColor Green

    try {
        $response = Invoke-RestMethod -Uri $url -Method Get
        Write-Host "数据获取成功" -ForegroundColor Green

        # 提取加密的题目数据
        $encryptedQuestions = $response.data.questions

        # 提取加密密钥
        $encryptedKey = $response.data.optional.qEncrypt.key

        Write-Host "加密密钥: $encryptedKey" -ForegroundColor Yellow
        Write-Host "正在尝试解密密钥..." -ForegroundColor Green

        # 尝试解密密钥的常见密码
        $possiblePasswords = @(
            "yuyiruankao",
            "ixunke",
            "questions",
            "qbank",
            "default",
            "123456",
            "password",
            "ruankao",
            "exam",
            "test"
        )
        
        $decryptedKey = $null
        foreach ($pwd in $possiblePasswords) {
            $result = Decrypt-CryptoJS -EncryptedData $encryptedKey -Password $pwd
            if ($null -ne $result -and $result.Length -gt 0) {
                Write-Host "密钥解密成功，使用密码: $pwd" -ForegroundColor Green
                Write-Host "解密后的密钥: $result" -ForegroundColor Yellow
                $decryptedKey = $result
                break
            }
        }
        
        if ($null -eq $decryptedKey) {
            Write-Host "无法解密密钥，尝试直接使用加密密钥..." -ForegroundColor Yellow
            $decryptedKey = $encryptedKey
        }
        
        Write-Host "正在尝试解密题目数据..." -ForegroundColor Green
        
        $decryptedQuestions = $null
        
        # 方法1: 使用解密后的密钥
        if ($decryptedKey -ne $encryptedKey) {
            $decryptedQuestions = Decrypt-CryptoJS -EncryptedData $encryptedQuestions -Password $decryptedKey
        }
        
        # 方法2: 如果方法1失败，尝试使用原始密钥
        if ($null -eq $decryptedQuestions) {
            $decryptedQuestions = Decrypt-CryptoJS -EncryptedData $encryptedQuestions -Password $encryptedKey
        }
        
        # 方法3: 尝试常见密码
        if ($null -eq $decryptedQuestions) {
            foreach ($pwd in $possiblePasswords) {
                $decryptedQuestions = Decrypt-CryptoJS -EncryptedData $encryptedQuestions -Password $pwd
                if ($null -ne $decryptedQuestions -and $decryptedQuestions.Length -gt 0) {
                    Write-Host "题目解密成功，使用密码: $pwd" -ForegroundColor Green
                    break
                }
            }
        }
        
        if ($null -ne $decryptedQuestions -and $decryptedQuestions.Length -gt 0) {
            Write-Host "题目数据解密成功！" -ForegroundColor Green
            
            try {
                # 尝试解析为JSON
                $questionsData = $decryptedQuestions | ConvertFrom-Json
                
                # 保存解密后的数据
                $outputFile = "decrypted_questions.json"
                $decryptedQuestions | Out-File -FilePath $outputFile -Encoding UTF8
                
                Write-Host "解密后的数据已保存到: $outputFile" -ForegroundColor Green
                
                if ($questionsData -is [Array]) {
                    Write-Host "题目数量: $($questionsData.Length)" -ForegroundColor Green
                } else {
                    Write-Host "题目数据类型: $($questionsData.GetType().Name)" -ForegroundColor Green
                }
                
                return $questionsData
            }
            catch {
                Write-Host "解密后的数据不是有效的JSON格式: $_" -ForegroundColor Red
                # 保存为文本文件
                $decryptedQuestions | Out-File -FilePath "decrypted_questions.txt" -Encoding UTF8
                Write-Host "解密后的数据已保存为文本文件: decrypted_questions.txt" -ForegroundColor Yellow
                return $decryptedQuestions
            }
        } else {
            Write-Host "题目数据解密失败" -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "获取数据失败: $_" -ForegroundColor Red
        return $null
    }
}

# 运行解密程序
Write-Host "开始解密题库数据..." -ForegroundColor Cyan
$result = Get-QuestionsData

if ($null -ne $result) {
    Write-Host "解密完成！" -ForegroundColor Green
} else {
    Write-Host "解密失败！" -ForegroundColor Red
}
