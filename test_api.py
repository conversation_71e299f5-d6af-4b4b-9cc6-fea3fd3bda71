#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API调用
"""

import requests
import json

def test_chapter_api():
    """测试章节API"""
    url = "https://api.ixunke.cn/zhangguangpu/api/chapter"
    params = {
        'qBankId': 5,
        'app': 'true',
        'token': '****************************************************************************************************************************************'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print("正在测试API调用...")
        response = requests.get(url, params=params, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, ensure_ascii=False, indent=2)[:1000] + "...")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求出错: {e}")

if __name__ == "__main__":
    test_chapter_api()
