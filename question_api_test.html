<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目API测试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .response-preview {
            background: #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 题目API专项测试</h1>
        
        <div class="form-group">
            <label for="tokenInput">API访问令牌:</label>
            <input type="text" id="tokenInput" 
                   value="VTJGc2RHVmtYMStrcGFhU2VveVAzUlJET2ZXUEVQOW03aDZRVW51Nkl6NnJvYWhRMFFteFUwa0tjT2F5L0M5ZlUwS3hPN1Q4c2YzNGVOdWlSMmlMZXc9PSMxNzUzNTk2MzYzMDQy">
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="chapterIdInput">章节ID:</label>
                <input type="number" id="chapterIdInput" value="99" placeholder="输入章节ID">
            </div>
            <div class="form-group">
                <label for="pageInput">页码:</label>
                <input type="number" id="pageInput" value="1" placeholder="页码">
            </div>
            <div class="form-group">
                <label for="pageSizeInput">每页数量:</label>
                <input type="number" id="pageSizeInput" value="10" placeholder="每页数量">
            </div>
        </div>
        
        <div>
            <button onclick="testQuestionAPI()" id="testBtn">测试题目API</button>
            <button onclick="testMultipleChapters()">测试多个章节</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="log-area" id="logArea"></div>
        
        <div id="responsePreview"></div>
    </div>

    <script>
        const API_BASE = 'https://api.ixunke.cn/zhangguangpu/api';
        const QBANK_ID = 5;
        
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            document.getElementById('responsePreview').innerHTML = '';
        }
        
        function showResponse(data) {
            const preview = document.getElementById('responsePreview');
            preview.innerHTML = `<h3>📄 完整响应数据:</h3><div class="response-preview">${JSON.stringify(data, null, 2)}</div>`;
        }
        
        async function testQuestionAPI() {
            const token = document.getElementById('tokenInput').value.trim();
            const chapterId = document.getElementById('chapterIdInput').value.trim();
            const page = document.getElementById('pageInput').value.trim();
            const pageSize = document.getElementById('pageSizeInput').value.trim();
            
            if (!token || !chapterId) {
                alert('请输入Token和章节ID');
                return;
            }
            
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            addLog(`🔍 开始测试题目API...`, 'info');
            addLog(`📋 参数: 章节ID=${chapterId}, 页码=${page}, 每页=${pageSize}`, 'info');
            
            try {
                const url = `${API_BASE}/question?qBankId=${QBANK_ID}&chapterId=${chapterId}&page=${page}&pageSize=${pageSize}&app=true&token=${encodeURIComponent(token)}`;
                addLog(`🌐 请求URL: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9'
                    }
                });
                
                addLog(`📊 HTTP状态: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addLog(`📄 响应解析成功`, 'success');
                
                // 显示完整响应
                showResponse(data);
                
                // 分析响应内容
                addLog(`🔍 分析响应内容:`, 'info');
                addLog(`  errno: ${data.errno}`, 'info');
                addLog(`  errmsg: ${data.errmsg || '无'}`, 'info');
                
                if (data.errno === 0) {
                    const questionData = data.data || {};
                    const questions = questionData.list || [];
                    const total = questionData.total || 0;
                    
                    addLog(`✅ API调用成功!`, 'success');
                    addLog(`📝 题目数据: 当前页${questions.length}道, 总共${total}道`, 'success');
                    
                    if (questions.length > 0) {
                        const firstQuestion = questions[0];
                        addLog(`📋 第一题示例:`, 'info');
                        addLog(`  ID: ${firstQuestion.id || '无'}`, 'info');
                        addLog(`  内容: ${(firstQuestion.content || '无内容').substring(0, 100)}...`, 'info');
                        addLog(`  答案: ${firstQuestion.answer || '无答案'}`, 'info');
                    } else {
                        addLog(`⚠ 该章节/页面没有题目`, 'warning');
                    }
                } else {
                    addLog(`❌ API返回错误: errno=${data.errno}, errmsg=${data.errmsg || '未知错误'}`, 'error');
                    
                    // 常见错误分析
                    if (data.errmsg && data.errmsg.includes('token')) {
                        addLog(`💡 建议: Token可能已过期，请更新Token`, 'warning');
                    } else if (data.errmsg && data.errmsg.includes('chapter')) {
                        addLog(`💡 建议: 章节ID可能无效，请检查章节ID`, 'warning');
                    }
                }
                
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                
                if (error.message.includes('CORS')) {
                    addLog(`💡 这是浏览器CORS限制，属于正常现象`, 'warning');
                    addLog(`💡 建议使用Python脚本进行实际下载`, 'warning');
                } else if (error.message.includes('fetch')) {
                    addLog(`💡 可能是网络连接问题`, 'warning');
                }
            } finally {
                btn.disabled = false;
                btn.textContent = '测试题目API';
            }
        }
        
        async function testMultipleChapters() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                alert('请输入Token');
                return;
            }
            
            // 测试几个常见的章节ID
            const testChapterIds = [45, 69, 76, 77, 99]; // 从之前的数据中选择
            
            addLog(`🔍 开始测试多个章节...`, 'info');
            
            for (const chapterId of testChapterIds) {
                addLog(`\n📝 测试章节ID: ${chapterId}`, 'info');
                
                try {
                    const url = `${API_BASE}/question?qBankId=${QBANK_ID}&chapterId=${chapterId}&page=1&pageSize=5&app=true&token=${encodeURIComponent(token)}`;
                    
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'Accept': 'application/json, text/plain, */*'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        
                        if (data.errno === 0) {
                            const total = data.data?.total || 0;
                            const questions = data.data?.list || [];
                            addLog(`  ✅ 成功: ${questions.length}/${total} 道题目`, 'success');
                        } else {
                            addLog(`  ❌ 失败: ${data.errmsg || '未知错误'}`, 'error');
                        }
                    } else {
                        addLog(`  ❌ HTTP错误: ${response.status}`, 'error');
                    }
                } catch (error) {
                    addLog(`  ❌ 请求异常: ${error.message}`, 'error');
                }
                
                // 请求间隔
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            addLog(`\n🎉 多章节测试完成`, 'info');
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            addLog('🚀 题目API测试工具已加载', 'info');
            addLog('💡 点击"测试题目API"开始测试特定章节', 'info');
            addLog('💡 点击"测试多个章节"批量测试多个章节', 'info');
        };
    </script>
</body>
</html>
