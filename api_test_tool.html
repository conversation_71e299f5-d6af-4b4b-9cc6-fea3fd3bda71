<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .chapter-preview {
            background: #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .chapter-item {
            padding: 5px 0;
            border-bottom: 1px solid #ccc;
        }
        
        .chapter-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API连接测试工具</h1>
        
        <div class="form-group">
            <label for="tokenInput">API访问令牌 (Token):</label>
            <input type="text" id="tokenInput" 
                   value="VTJGc2RHVmtYMStrcGFhU2VveVAzUlJET2ZXUEVQOW03aDZRVW51Nkl6NnJvYWhRMFFteFUwa0tjT2F5L0M5ZlUwS3hPN1Q4c2YzNGVOdWlSMmlMZXc9PSMxNzUzNTk2MzYzMDQy"
                   placeholder="请输入API访问令牌...">
        </div>
        
        <div>
            <button onclick="testChapterAPI()" id="testChapterBtn">测试章节API</button>
            <button onclick="testQuestionAPI()" id="testQuestionBtn" disabled>测试题目API</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="statusArea"></div>
        
        <div class="log-area" id="logArea"></div>
        
        <div id="chapterPreview"></div>
    </div>

    <script>
        const API_BASE = 'https://api.ixunke.cn/zhangguangpu/api';
        const QBANK_ID = 5;
        let testChapterId = null;
        
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            document.getElementById('statusArea').innerHTML = '';
            document.getElementById('chapterPreview').innerHTML = '';
        }
        
        function showStatus(message, isSuccess) {
            const statusArea = document.getElementById('statusArea');
            statusArea.innerHTML = `<div class="status ${isSuccess ? 'status-success' : 'status-error'}">${message}</div>`;
        }
        
        async function testChapterAPI() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                alert('请输入API访问令牌');
                return;
            }
            
            const btn = document.getElementById('testChapterBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            addLog('🔍 开始测试章节API...', 'info');
            
            try {
                // 方法1: 直接请求（可能被CORS阻止）
                addLog('📡 尝试直接API请求...', 'info');
                const url = `${API_BASE}/chapter?qBankId=${QBANK_ID}&app=true&token=${encodeURIComponent(token)}`;
                addLog(`🌐 请求URL: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json, text/plain, */*'
                    }
                });
                
                addLog(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    addLog(`📄 响应数据: errno=${data.errno}`, 'info');
                    
                    if (data.errno === 0) {
                        const chapters = data.data || [];
                        const chaptersWithQuestions = chapters.filter(ch => (ch.questionCount || 0) > 0);
                        
                        addLog(`✅ 成功获取 ${chapters.length} 个章节`, 'success');
                        addLog(`📝 其中 ${chaptersWithQuestions.length} 个章节有题目`, 'success');
                        
                        showStatus(`✅ 章节API测试成功！获取到 ${chaptersWithQuestions.length} 个有题目的章节`, true);
                        
                        // 显示章节预览
                        showChapterPreview(chaptersWithQuestions.slice(0, 10));
                        
                        // 启用题目API测试
                        if (chaptersWithQuestions.length > 0) {
                            testChapterId = chaptersWithQuestions[0].id;
                            document.getElementById('testQuestionBtn').disabled = false;
                            addLog(`🎯 将使用章节 "${chaptersWithQuestions[0].title}" (ID: ${testChapterId}) 测试题目API`, 'info');
                        }
                        
                    } else {
                        addLog(`❌ API返回错误: ${data.errmsg || '未知错误'}`, 'error');
                        showStatus(`❌ API返回错误: ${data.errmsg || '未知错误'}`, false);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`, 'error');
                
                if (error.message.includes('CORS') || error.message.includes('fetch')) {
                    addLog('⚠ 可能是CORS跨域问题，这在浏览器中是正常的', 'warning');
                    addLog('💡 建议使用Python脚本进行实际下载', 'warning');
                    showStatus('⚠ 浏览器CORS限制，建议使用Python脚本', false);
                } else {
                    showStatus(`❌ 测试失败: ${error.message}`, false);
                }
            } finally {
                btn.disabled = false;
                btn.textContent = '测试章节API';
            }
        }
        
        async function testQuestionAPI() {
            if (!testChapterId) {
                addLog('❌ 请先成功测试章节API', 'error');
                return;
            }
            
            const token = document.getElementById('tokenInput').value.trim();
            const btn = document.getElementById('testQuestionBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            addLog(`🔍 开始测试题目API (章节ID: ${testChapterId})...`, 'info');
            
            try {
                const url = `${API_BASE}/question?qBankId=${QBANK_ID}&chapterId=${testChapterId}&page=1&pageSize=5&app=true&token=${encodeURIComponent(token)}`;
                addLog(`🌐 请求URL: ${url}`, 'info');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'application/json, text/plain, */*'
                    }
                });
                
                addLog(`📊 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.errno === 0) {
                        const questionData = data.data || {};
                        const questions = questionData.list || [];
                        const total = questionData.total || 0;
                        
                        addLog(`✅ 成功获取 ${questions.length} 道题目 (总共 ${total} 道)`, 'success');
                        
                        if (questions.length > 0) {
                            const firstQuestion = questions[0];
                            addLog(`📋 第一题示例: ${(firstQuestion.content || '').substring(0, 50)}...`, 'info');
                            showStatus(`✅ 题目API测试成功！该章节共有 ${total} 道题目`, true);
                        }
                        
                    } else {
                        addLog(`❌ API返回错误: ${data.errmsg || '未知错误'}`, 'error');
                        showStatus(`❌ 题目API错误: ${data.errmsg || '未知错误'}`, false);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                addLog(`❌ 题目API请求失败: ${error.message}`, 'error');
                showStatus(`❌ 题目API测试失败: ${error.message}`, false);
            } finally {
                btn.disabled = false;
                btn.textContent = '测试题目API';
            }
        }
        
        function showChapterPreview(chapters) {
            const preview = document.getElementById('chapterPreview');
            if (chapters.length === 0) return;
            
            let html = '<h3>📚 章节预览 (前10个)</h3><div class="chapter-preview">';
            chapters.forEach((chapter, index) => {
                html += `<div class="chapter-item">${index + 1}. ${chapter.title} (ID: ${chapter.id}, 题目: ${chapter.questionCount || 0})</div>`;
            });
            html += '</div>';
            
            preview.innerHTML = html;
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            addLog('🚀 API测试工具已加载', 'info');
            addLog('💡 点击"测试章节API"开始测试', 'info');
        };
    </script>
</body>
</html>
