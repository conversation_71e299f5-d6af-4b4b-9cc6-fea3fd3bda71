# 章节题库下载工具使用说明

## 📚 工具介绍

本工具包含Python脚本和HTML网页工具两种方式，用于从指定的API获取章节信息并下载各个章节的题库数据，保存为JSON文件。

## 🛠 工具文件

1. **chapter_downloader.py** - Python命令行脚本
2. **chapter_downloader_tool.html** - HTML网页工具（推荐）
3. **test_download.py** - API连接测试脚本

## 🌐 HTML网页工具使用方法（推荐）

### 步骤1：打开工具
- 双击 `chapter_downloader_tool.html` 文件
- 或在浏览器中打开该文件

### 步骤2：输入Token
- 在"API访问令牌"输入框中输入您的token
- 默认已填入提供的token，如需更新请替换

### 步骤3：获取章节列表
- 点击"获取章节列表"按钮
- 等待系统获取并显示所有包含题目的章节

### 步骤4：开始下载
- 点击"开始下载"按钮
- 系统将自动下载所有章节的题目数据
- 可以随时点击"停止下载"中断下载

### 步骤5：查看结果
- 下载完成后会显示统计信息
- 每个章节的JSON文件会自动下载到浏览器的下载目录
- 同时会生成一个 `download_summary.json` 摘要文件

## 💻 Python脚本使用方法

### 基本用法
```bash
python chapter_downloader.py
```

### 自定义参数
```bash
# 使用自定义token
python chapter_downloader.py --token "你的token"

# 指定输出目录
python chapter_downloader.py --output "输出目录"

# 组合使用
python chapter_downloader.py --token "你的token" --output "my_questions"
```

### 测试API连接
```bash
python test_download.py
```

## 📁 输出文件说明

### 章节JSON文件
每个章节会生成一个JSON文件，文件名为章节标题，包含以下结构：
```json
{
  "chapter_id": 123,
  "chapter_title": "章节名称",
  "total_questions": 50,
  "download_time": "2024-01-01T12:00:00",
  "questions": [
    {
      "id": 1,
      "content": "题目内容...",
      "answer": "答案",
      "analysis": "解析",
      // 其他题目字段...
    }
  ]
}
```

### 下载摘要文件
`download_summary.json` 包含下载统计信息：
```json
{
  "download_time": "2024-01-01T12:00:00",
  "total_chapters": 30,
  "success_count": 28,
  "failed_count": 2,
  "total_questions": 1500,
  "failed_chapters": ["失败的章节1", "失败的章节2"],
  "chapters_info": [
    {
      "id": 123,
      "title": "章节名称",
      "questionCount": 50
    }
  ]
}
```

## ⚙️ 配置说明

### API配置
- **基础URL**: `https://api.ixunke.cn/zhangguangpu/api`
- **题库ID**: `5` (系统规划师)
- **Token**: 需要有效的访问令牌

### 下载设置
- **分页大小**: 每页100道题目
- **请求间隔**: 章节间隔1秒，分页间隔0.5秒
- **超时时间**: 30秒

## 🔧 故障排除

### 常见问题

1. **Token过期**
   - 症状：返回"token已超时"错误
   - 解决：更新token参数

2. **网络连接问题**
   - 症状：请求超时或连接失败
   - 解决：检查网络连接，重试下载

3. **章节无题目**
   - 症状：某些章节显示0道题目
   - 说明：该章节确实没有题目，属于正常情况

4. **文件名包含特殊字符**
   - 症状：文件保存失败
   - 解决：系统会自动清理文件名中的特殊字符

### Python环境问题
如果Python脚本无法运行，请检查：
1. Python版本是否为3.6+
2. 是否安装了requests库：`pip install requests`
3. 网络防火墙设置

## 📊 功能特点

### ✅ 支持的功能
- 自动获取章节列表
- 批量下载所有章节题目
- 分页处理大量题目
- 自动重试失败的请求
- 生成下载统计报告
- 支持中断和恢复下载
- 文件名自动清理

### 🔄 下载流程
1. 获取章节列表
2. 过滤包含题目的章节
3. 逐个下载章节题目
4. 分页获取所有题目
5. 保存为JSON文件
6. 生成下载摘要

## 📝 注意事项

1. **请求频率**: 工具已设置合理的请求间隔，避免对服务器造成压力
2. **文件大小**: 题目较多的章节生成的JSON文件可能较大
3. **网络稳定性**: 建议在网络稳定的环境下使用
4. **存储空间**: 确保有足够的磁盘空间存储下载的文件

## 🆘 技术支持

如果遇到问题：
1. 首先运行 `test_download.py` 测试API连接
2. 检查token是否有效
3. 确认网络连接正常
4. 查看错误日志信息

## 📈 更新日志

- **v1.0**: 初始版本
  - 支持章节列表获取
  - 支持批量题目下载
  - 提供Python和HTML两种工具
  - 包含完整的错误处理和日志记录
