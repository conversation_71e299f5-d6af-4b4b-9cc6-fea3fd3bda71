# JSON转CSV工具使用说明

## 📊 工具介绍

本工具专门用于将下载的JSON格式题目数据转换为CSV格式，按照指定的列格式输出：
- **题目**（stem字段）
- **选项**（options字段，格式化为A、B、C、D选项）
- **答案**（answer字段，将索引转换为字母）
- **答案解析**（analysis字段）

## 🛠 工具文件

1. **json_to_csv_tool.html** - HTML网页工具（推荐，已在浏览器中打开）
2. **json_to_csv_converter.py** - Python命令行工具

## 🌐 HTML网页工具使用方法（推荐）

### 步骤1：选择文件
- 拖拽JSON文件到上传区域
- 或点击"选择文件"按钮选择JSON文件
- 支持同时选择多个JSON文件

### 步骤2：开始转换
- 点击"开始转换"按钮
- 等待转换完成

### 步骤3：下载结果
- 转换完成后，点击"下载CSV文件"链接
- CSV文件会自动下载到浏览器下载目录

## 💻 Python命令行工具使用方法

### 单文件转换
```bash
python json_to_csv_converter.py -i "输入文件.json" -o "输出文件.csv"
```

### 批量转换
```bash
python json_to_csv_converter.py -i "输入目录" -o "输出目录" --batch
```

### 示例
```bash
# 转换单个文件
python json_to_csv_converter.py -i "第10章_项目进度管理.json"

# 批量转换questions目录中的所有JSON文件
python json_to_csv_converter.py -i "questions" -o "csv_output" --batch
```

## 📁 支持的JSON格式

工具能够自动识别以下JSON结构：

### 格式1：标准题库格式
```json
{
  "data": {
    "questions": [
      {
        "stem": "题目内容",
        "options": ["选项A", "选项B", "选项C", "选项D"],
        "answer": [0],
        "analysis": "答案解析"
      }
    ]
  }
}
```

### 格式2：简化格式
```json
{
  "questions": [
    {
      "stem": "题目内容",
      "options": ["选项A", "选项B", "选项C", "选项D"],
      "answer": 0,
      "analysis": "答案解析"
    }
  ]
}
```

### 格式3：直接数组格式
```json
[
  {
    "stem": "题目内容",
    "options": ["选项A", "选项B", "选项C", "选项D"],
    "answer": [0],
    "analysis": "答案解析"
  }
]
```

## 📋 输出格式说明

### CSV列结构
| 题目 | 选项 | 答案 | 答案解析 |
|------|------|------|----------|
| 清理后的题目内容 | A. 选项1 \| B. 选项2 \| C. 选项3 \| D. 选项4 | A | 清理后的解析内容 |

### 数据处理规则

#### 1. 题目处理
- 提取`stem`或`content`字段
- 自动移除HTML标签（如`<p>`, `<br>`等）
- 清理多余的空白字符和换行符

#### 2. 选项处理
- 将选项数组格式化为字符串
- 格式：`A. 选项1 | B. 选项2 | C. 选项3 | D. 选项4`
- 自动清理HTML标签

#### 3. 答案处理
- 支持索引格式：`[0]` → `A`，`[1]` → `B`
- 支持数字格式：`0` → `A`，`1` → `B`
- 支持多选：`[0, 2]` → `A, C`
- 支持字符串格式：直接使用

#### 4. 解析处理
- 提取`analysis`或`explanation`字段
- 自动移除HTML标签
- 清理格式

## 🔧 特殊功能

### HTML标签清理
自动移除常见的HTML标签：
- `<p>`, `</p>`
- `<br>`, `<br/>`
- `<div>`, `</div>`
- `<span>`, `</span>`
- 等等

### 编码处理
- 输出CSV使用UTF-8 with BOM编码
- 确保Excel能正确显示中文字符
- 自动处理CSV中的特殊字符转义

### 错误处理
- 自动跳过无法解析的题目
- 提供详细的错误信息
- 统计转换成功的题目数量

## 📊 转换示例

### 输入JSON
```json
{
  "data": {
    "questions": [
      {
        "stem": "<p>项目管理的核心是什么？</p>",
        "options": [
          "<p>时间管理</p>",
          "<p>成本管理</p>",
          "<p>整合管理</p>",
          "<p>质量管理</p>"
        ],
        "answer": [2],
        "analysis": "<p>整合管理是项目管理的核心。</p>"
      }
    ]
  }
}
```

### 输出CSV
```csv
题目,选项,答案,答案解析
项目管理的核心是什么？,A. 时间管理 | B. 成本管理 | C. 整合管理 | D. 质量管理,C,整合管理是项目管理的核心。
```

## ⚠ 注意事项

1. **文件编码**：确保JSON文件使用UTF-8编码
2. **文件大小**：大文件可能需要较长处理时间
3. **浏览器兼容性**：HTML工具需要现代浏览器支持
4. **数据完整性**：转换前请备份原始JSON文件

## 🆘 故障排除

### 常见问题

1. **无法找到题目数据**
   - 检查JSON文件结构是否正确
   - 确认包含`questions`、`stem`等字段

2. **中文显示乱码**
   - 使用支持UTF-8的文本编辑器打开CSV
   - 在Excel中导入时选择UTF-8编码

3. **选项格式错误**
   - 检查`options`字段是否为数组格式
   - 确认选项内容不为空

4. **答案转换错误**
   - 检查`answer`字段格式
   - 确认索引值在有效范围内（0-5）

## 📈 更新日志

- **v1.0**: 初始版本
  - 支持标准JSON格式转换
  - 提供HTML和Python两种工具
  - 自动HTML标签清理
  - 完整的错误处理机制
