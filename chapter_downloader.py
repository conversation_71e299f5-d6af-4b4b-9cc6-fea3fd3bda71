#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节题库下载器
从指定的API获取章节列表，然后下载每个章节的题目并保存为JSON文件
"""

import requests
import json
import time
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ChapterDownloader:
    def __init__(self, token: str = None):
        self.base_url = "https://api.ixunke.cn"
        self.qbank_id = 5
        self.token = token or "****************************************************************************************************************************************"

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
        }

        # 确保输出目录存在
        self.output_dir = "questions"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def get_chapters(self) -> List[Dict[str, Any]]:
        """获取章节列表"""
        print("📚 正在获取章节列表...")
        
        url = f"{self.base_url}/zhangguangpu/api/chapter"
        params = {
            'qBankId': self.qbank_id,
            'app': 'true',
            'token': self.token
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, verify=False, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('errno') == 0:
                chapters = data.get('data', [])
                print(f"✅ 成功获取 {len(chapters)} 个章节")
                return chapters
            else:
                print(f"❌ 获取章节失败: {data.get('errmsg', '未知错误')}")
                return []
                
        except Exception as e:
            print(f"❌ 请求章节列表时出错: {e}")
            return []
    
    def flatten_chapters(self, chapters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """扁平化章节列表，包括子章节"""
        flattened = []
        
        for chapter in chapters:
            # 添加当前章节（如果有题目）
            if chapter.get('questionCount', 0) > 0:
                flattened.append(chapter)
            
            # 递归处理子章节
            children = chapter.get('children', [])
            if children:
                flattened.extend(self.flatten_chapters(children))
        
        return flattened
    
    def get_questions_for_chapter(self, chapter_id: int, chapter_title: str) -> Optional[Dict[str, Any]]:
        """获取指定章节的所有题目"""
        print(f"📝 正在获取章节: {chapter_title} (ID: {chapter_id})")
        
        all_questions = []
        page = 1
        page_size = 100
        
        while True:
            questions_data = self.get_questions_page(chapter_id, page, page_size)
            
            if not questions_data:
                break
            
            questions = questions_data.get('list', [])
            if not questions:
                break
            
            all_questions.extend(questions)
            
            # 检查是否还有更多页面
            total = questions_data.get('total', 0)
            current_count = len(all_questions)
            
            print(f"  已获取 {current_count}/{total} 题目")
            
            if current_count >= total:
                break
            
            page += 1
            time.sleep(0.5)  # 避免请求过快
        
        if all_questions:
            print(f"  ✅ 章节 {chapter_title} 共获取 {len(all_questions)} 题目")
            return {
                "chapter_id": chapter_id,
                "chapter_title": chapter_title,
                "total_questions": len(all_questions),
                "download_time": datetime.now().isoformat(),
                "questions": all_questions
            }
        else:
            print(f"  ⚠ 章节 {chapter_title} 未获取到题目")
            return None
    
    def get_questions_page(self, chapter_id: int, page: int, page_size: int) -> Optional[Dict[str, Any]]:
        """获取指定章节的题目（分页）"""
        url = f"{self.base_url}/zhangguangpu/api/question"
        params = {
            'qBankId': self.qbank_id,
            'chapterId': chapter_id,
            'page': page,
            'pageSize': page_size,
            'app': 'true',
            'token': self.token
        }
        
        try:
            response = requests.get(url, params=params, headers=self.headers, verify=False, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('errno') == 0:
                return data.get('data', {})
            else:
                print(f"    ⚠ 获取第 {page} 页失败: {data.get('errmsg', '未知错误')}")
                return None
                
        except Exception as e:
            print(f"    ❌ 请求第 {page} 页时出错: {e}")
            return None
    
    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除多余的空格和点
        filename = re.sub(r'\s+', ' ', filename).strip()
        filename = filename.strip('.')
        
        # 如果文件名为空或只有下划线，使用默认名称
        if not filename or filename.replace('_', '').strip() == '':
            filename = "未命名章节"
        
        return filename
    
    def save_chapter_data(self, chapter_data: Dict[str, Any]) -> bool:
        """保存章节数据到JSON文件"""
        if not chapter_data:
            return False
        
        # 生成安全的文件名
        chapter_title = chapter_data.get('chapter_title', '未命名章节')
        safe_filename = self.clean_filename(chapter_title)
        filepath = os.path.join(self.output_dir, f"{safe_filename}.json")
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(chapter_data, f, ensure_ascii=False, indent=2)
            
            print(f"  💾 已保存到: {filepath}")
            return True
            
        except Exception as e:
            print(f"  ❌ 保存失败: {e}")
            return False
    
    def download_all_chapters(self) -> Dict[str, Any]:
        """下载所有章节的题目"""
        print("🎓 开始下载章节题库...")
        print("=" * 60)

        # 获取章节列表
        chapters = self.get_chapters()
        if not chapters:
            print("❌ 无法获取章节列表，程序退出")
            return {"success": False, "error": "无法获取章节列表"}

        # 扁平化章节列表（包括子章节）
        flat_chapters = self.flatten_chapters(chapters)

        if not flat_chapters:
            print("❌ 没有找到包含题目的章节")
            return {"success": False, "error": "没有找到包含题目的章节"}

        print(f"📋 找到 {len(flat_chapters)} 个包含题目的章节")
        print("-" * 60)

        # 下载统计
        success_count = 0
        total_questions = 0
        failed_chapters = []

        # 逐个下载章节
        for i, chapter in enumerate(flat_chapters, 1):
            chapter_id = chapter.get('id')
            chapter_title = chapter.get('title', f"章节{i}")
            question_count = chapter.get('questionCount', 0)

            print(f"\n[{i}/{len(flat_chapters)}] 处理章节: {chapter_title}")
            print(f"  预期题目数量: {question_count}")

            # 获取章节题目
            chapter_data = self.get_questions_for_chapter(chapter_id, chapter_title)

            if chapter_data and self.save_chapter_data(chapter_data):
                success_count += 1
                total_questions += chapter_data['total_questions']
            else:
                failed_chapters.append(chapter_title)

            # 请求间隔，避免过于频繁
            if i < len(flat_chapters):
                time.sleep(1)

        # 输出下载结果
        print("\n" + "=" * 60)
        print("🎉 下载完成!")
        print(f"✅ 成功下载: {success_count}/{len(flat_chapters)} 个章节")
        print(f"📝 总题目数量: {total_questions} 道")
        print(f"📁 文件保存目录: {self.output_dir}/")

        if failed_chapters:
            print(f"\n❌ 下载失败的章节:")
            for chapter in failed_chapters:
                print(f"  - {chapter}")

        # 保存下载摘要
        summary = self.save_download_summary(flat_chapters, success_count, total_questions, failed_chapters)

        return {
            "success": True,
            "total_chapters": len(flat_chapters),
            "success_count": success_count,
            "total_questions": total_questions,
            "failed_chapters": failed_chapters,
            "summary": summary
        }
    
    def save_download_summary(self, chapters: List[Dict], success_count: int,
                            total_questions: int, failed_chapters: List[str]) -> Dict[str, Any]:
        """保存下载摘要"""
        summary = {
            "download_time": datetime.now().isoformat(),
            "total_chapters": len(chapters),
            "success_count": success_count,
            "failed_count": len(failed_chapters),
            "total_questions": total_questions,
            "failed_chapters": failed_chapters,
            "chapters_info": [
                {
                    "id": ch.get('id'),
                    "title": ch.get('title'),
                    "questionCount": ch.get('questionCount', 0)
                }
                for ch in chapters
            ]
        }

        summary_file = os.path.join(self.output_dir, "download_summary.json")
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            print(f"📊 下载摘要已保存到: {summary_file}")
        except Exception as e:
            print(f"⚠ 保存下载摘要失败: {e}")

        return summary

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='章节题库下载器')
    parser.add_argument('--token', '-t', help='API访问令牌')
    parser.add_argument('--output', '-o', default='questions', help='输出目录')

    args = parser.parse_args()

    downloader = ChapterDownloader(token=args.token)
    if args.output != 'questions':
        downloader.output_dir = args.output
        if not os.path.exists(args.output):
            os.makedirs(args.output)

    result = downloader.download_all_chapters()

    if result["success"]:
        print(f"\n✨ 任务完成！成功下载 {result['success_count']} 个章节，共 {result['total_questions']} 道题目")
    else:
        print(f"\n❌ 任务失败：{result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()
