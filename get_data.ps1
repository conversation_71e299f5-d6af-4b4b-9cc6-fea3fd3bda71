# 获取并分析题库数据

$url = "https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification?app=true&token=**************************************************************************************************************************************************************************%3D%3D&qBankId=4&chapterId=62&studentAnswer=1"

Write-Host "正在获取数据..." -ForegroundColor Green

try {
    $response = Invoke-RestMethod -Uri $url -Method Get
    Write-Host "数据获取成功" -ForegroundColor Green
    
    # 保存原始响应数据
    $response | ConvertTo-Json -Depth 10 | Out-File -FilePath "raw_response.json" -Encoding UTF8
    Write-Host "原始响应数据已保存到: raw_response.json" -ForegroundColor Yellow
    
    # 提取关键信息
    $encryptedQuestions = $response.data.questions
    $encryptedKey = $response.data.optional.qEncrypt.key
    
    Write-Host "加密密钥: $encryptedKey" -ForegroundColor Yellow
    Write-Host "加密题目数据长度: $($encryptedQuestions.Length)" -ForegroundColor Yellow
    
    # 创建加密数据对象
    $encryptedData = @{
        encryptedKey = $encryptedKey
        encryptedQuestions = $encryptedQuestions
        encryptConfig = $response.data.optional.qEncrypt
    }
    
    # 保存加密数据
    $encryptedData | ConvertTo-Json -Depth 5 | Out-File -FilePath "encrypted_data.json" -Encoding UTF8
    Write-Host "加密数据已保存到: encrypted_data.json" -ForegroundColor Yellow
    
    Write-Host "数据获取完成" -ForegroundColor Green
    
} catch {
    Write-Host "获取数据失败: $($_.Exception.Message)" -ForegroundColor Red
}
