<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON转CSV工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #4facfe;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #4facfe;
            background-color: #e3f2fd;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .file-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .preview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .preview-table th,
        .preview-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .preview-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 15px;
            color: #4facfe;
            text-decoration: none;
            font-weight: bold;
        }
        
        .download-link:hover {
            text-decoration: underline;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 10px 10px 0;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 JSON转CSV工具</h1>
            <p>将JSON格式的题目数据转换为CSV格式</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 转换格式说明</h3>
                <ul>
                    <li><strong>输出列：</strong>题目、选项、答案、答案解析</li>
                    <li><strong>题目：</strong>提取stem字段，自动清理HTML标签</li>
                    <li><strong>选项：</strong>格式化为 "A. 选项1 | B. 选项2 | C. 选项3 | D. 选项4"</li>
                    <li><strong>答案：</strong>将索引转换为字母（0→A, 1→B, 2→C, 3→D）</li>
                    <li><strong>答案解析：</strong>提取analysis字段，清理HTML标签</li>
                </ul>
            </div>
            
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">拖拽JSON文件到此处，或点击选择文件</div>
                <button class="btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
                <input type="file" id="fileInput" class="file-input" accept=".json" multiple>
            </div>
            
            <div class="file-info" id="fileInfo">
                <div class="file-name" id="fileName"></div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <button class="btn" id="convertBtn" onclick="convertFiles()">开始转换</button>
            </div>
            
            <div class="result-area" id="resultArea"></div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        
        // 文件上传区域事件
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const resultArea = document.getElementById('resultArea');
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        function handleFiles(files) {
            selectedFiles = Array.from(files).filter(file => file.name.endsWith('.json'));
            
            if (selectedFiles.length === 0) {
                alert('请选择JSON格式的文件');
                return;
            }
            
            const fileNames = selectedFiles.map(f => f.name).join(', ');
            fileName.textContent = `已选择 ${selectedFiles.length} 个文件: ${fileNames}`;
            fileInfo.style.display = 'block';
            resultArea.style.display = 'none';
        }
        
        function convertFiles() {
            if (selectedFiles.length === 0) return;
            
            const convertBtn = document.getElementById('convertBtn');
            const progressFill = document.getElementById('progressFill');
            
            convertBtn.disabled = true;
            convertBtn.textContent = '转换中...';
            
            let completedFiles = 0;
            const results = [];
            
            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    try {
                        const jsonData = JSON.parse(e.target.result);
                        const csvData = convertJSONToCSV(jsonData);
                        
                        if (csvData) {
                            results.push({
                                fileName: file.name,
                                csvData: csvData,
                                success: true,
                                questionCount: csvData.split('\n').length - 1,
                                index: index
                            });
                        } else {
                            results.push({
                                fileName: file.name,
                                success: false,
                                error: '无法解析题目数据',
                                index: index
                            });
                        }
                    } catch (error) {
                        results.push({
                            fileName: file.name,
                            success: false,
                            error: error.message,
                            index: index
                        });
                    }
                    
                    completedFiles++;
                    const progress = (completedFiles / selectedFiles.length) * 100;
                    progressFill.style.width = progress + '%';
                    
                    if (completedFiles === selectedFiles.length) {
                        showResults(results);
                        convertBtn.disabled = false;
                        convertBtn.textContent = '开始转换';
                    }
                };
                
                reader.readAsText(file, 'utf-8');
            });
        }
        
        function convertJSONToCSV(jsonData) {
            // 提取题目数据
            let questions = [];
            
            if (typeof jsonData === 'object' && jsonData !== null) {
                // 检查不同的数据结构
                if (jsonData.data && jsonData.data.questions) {
                    questions = jsonData.data.questions;
                } else if (jsonData.data && Array.isArray(jsonData.data)) {
                    questions = jsonData.data;
                } else if (jsonData.questions) {
                    questions = jsonData.questions;
                } else if (jsonData.list) {
                    questions = jsonData.list;
                } else if (Array.isArray(jsonData)) {
                    questions = jsonData;
                } else if (jsonData.stem || jsonData.content) {
                    questions = [jsonData];
                }
            }
            
            if (questions.length === 0) {
                return null;
            }
            
            // CSV头部
            const headers = ['题目', '选项', '答案', '答案解析'];
            let csvContent = headers.join(',') + '\n';
            
            // 转换每个题目
            questions.forEach(question => {
                const row = processQuestion(question);
                const csvRow = headers.map(header => {
                    const value = row[header] || '';
                    // 转义CSV中的特殊字符
                    return '"' + value.replace(/"/g, '""') + '"';
                }).join(',');
                csvContent += csvRow + '\n';
            });
            
            return csvContent;
        }
        
        function processQuestion(question) {
            // 提取题目内容
            const stem = cleanHTML(question.stem || question.content || '');
            
            // 提取选项
            const options = question.options || [];
            const formattedOptions = formatOptions(options);
            
            // 提取答案
            const answer = question.answer || '';
            const formattedAnswer = formatAnswer(answer);
            
            // 提取解析
            const analysis = cleanHTML(question.analysis || question.explanation || '');
            
            return {
                '题目': stem,
                '选项': formattedOptions,
                '答案': formattedAnswer,
                '答案解析': analysis
            };
        }
        
        function cleanHTML(text) {
            if (!text) return '';
            // 移除HTML标签
            text = text.replace(/<[^>]+>/g, '');
            // 移除多余空白
            text = text.replace(/\s+/g, ' ').trim();
            // 移除换行符
            text = text.replace(/[\r\n]/g, ' ');
            return text;
        }
        
        function formatOptions(options) {
            if (!options || options.length === 0) return '';
            
            const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];
            const formattedOptions = [];
            
            options.forEach((option, index) => {
                if (index < optionLabels.length) {
                    const cleanOption = cleanHTML(option);
                    if (cleanOption) {
                        formattedOptions.push(`${optionLabels[index]}. ${cleanOption}`);
                    }
                }
            });
            
            return formattedOptions.join(' | ');
        }
        
        function formatAnswer(answer) {
            if (!answer) return '';
            
            const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            // 如果答案是数组（索引形式）
            if (Array.isArray(answer)) {
                const answerLetters = [];
                answer.forEach(idx => {
                    if (typeof idx === 'number' && idx >= 0 && idx < optionLabels.length) {
                        answerLetters.push(optionLabels[idx]);
                    }
                });
                return answerLetters.join(', ');
            }
            
            // 如果答案是数字
            if (typeof answer === 'number' && answer >= 0 && answer < optionLabels.length) {
                return optionLabels[answer];
            }
            
            // 如果答案是字符串
            if (typeof answer === 'string') {
                return cleanHTML(answer);
            }
            
            return String(answer);
        }
        
        function showResults(results) {
            resultArea.innerHTML = '';
            resultArea.style.display = 'block';
            
            results.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = result.success ? 'success' : 'error';
                resultDiv.style.marginBottom = '15px';
                resultDiv.style.padding = '15px';
                resultDiv.style.borderRadius = '8px';
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <strong>✅ ${result.fileName}</strong><br>
                        成功转换 ${result.questionCount} 道题目<br>
                        <button class="btn" onclick="downloadCSVFile('${result.fileName}', ${result.index})">
                            📥 下载CSV文件
                        </button>
                    `;

                    // 将CSV数据存储到全局变量中
                    if (!window.csvResults) {
                        window.csvResults = {};
                    }
                    window.csvResults[result.index] = result.csvData;
                } else {
                    resultDiv.innerHTML = `
                        <strong>❌ ${result.fileName}</strong><br>
                        转换失败: ${result.error}
                    `;
                }
                
                resultArea.appendChild(resultDiv);
            });
        }
        
        function downloadCSVFile(fileName, index) {
            try {
                // 从全局变量获取CSV数据
                if (!window.csvResults || !window.csvResults[index]) {
                    alert('CSV数据不存在，请重新转换');
                    return;
                }

                const csvData = window.csvResults[index];

                // 创建CSV内容，添加BOM以支持Excel中文显示
                const csvContent = '\ufeff' + csvData;
                const blob = new Blob([csvContent], {
                    type: 'text/csv;charset=utf-8;'
                });

                // 生成文件名
                const csvFileName = fileName.replace('.json', '.csv');

                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = csvFileName;
                link.style.display = 'none';

                // 触发下载
                document.body.appendChild(link);
                link.click();

                // 清理
                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                }, 100);

                console.log(`文件 ${csvFileName} 下载成功`);
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败: ' + error.message);
            }
        }

        // 备用下载函数
        function downloadCSV(fileName, csvData) {
            try {
                const csvContent = '\ufeff' + csvData;
                const blob = new Blob([csvContent], {
                    type: 'text/csv;charset=utf-8;'
                });

                const csvFileName = fileName.replace('.json', '.csv');
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = csvFileName;
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();

                setTimeout(() => {
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                }, 100);

            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
