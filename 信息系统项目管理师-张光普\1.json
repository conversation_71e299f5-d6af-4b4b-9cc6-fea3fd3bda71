{"count": 60, "totalPages": 1, "pageSize": 60, "currentPage": 1, "data": {"questions": [{"id": 4272, "type": "1", "stem": "<p>【教材练习】-信息的概念</p>\n<p>信息的基础是(  )。</p>\n", "answer": [0], "analysis": "<p>信息的基础是数据。</p>\n", "options": ["<p>数据</p>\n", "<p>知识</p>\n", "<p>事实</p>\n", "<p>概念</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1488, "rightCount": 1430, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4273, "type": "1", "stem": "<p>【模拟题】-信息的概念</p>\n<p>下列说法正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P1-2，信息是物质、能量及其属性的标示的集合，是确定性的增加，所有B错误。信息不是物质，也不是能量，它以一种普遍形式，表达物质运动规律，在客观世界中大量存在、产生和传递，所有C错误。</p>\n<p>香农指出:&quot;信息是用来消除随机不定性的东西&quot;，所有D错误。</p>\n", "options": ["<p>信息是指音讯、消息、信息系统传输和处理的对象，泛指人类社会传播的一切内容</p>\n", "<p>信息是物质、能量及其属性的标示的集合，是确定性的减少</p>\n", "<p>信息是物质,但不是能量,它以一种普遍形式,表达物质运动规律,在客观世界中大量存在、产生和传递</p>\n", "<p>香农指出:&quot;信息是用来消除确定性的东西&quot;</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1495, "rightCount": 1104, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4274, "type": "1", "stem": "<p>【模拟题】-信息的概念</p>\n<p>下列说法中不正确的是(  )。</p>\n", "answer": [1], "analysis": "<p>P1-3，信息具有价值，而价值的大小决定于信息的质量</p>\n", "options": ["<p>信息的目的是用来&quot;消除不确定的因素&quot;</p>\n", "<p>信息具有价值，而价值的大小决定于信息的数量</p>\n", "<p>信息系统是由相互联系、相互依赖、相互作用的事物或过程组成的具有整体功能和综合行为的统一体</p>\n", "<p>简单地说，信息系统就是通过输入数据，然后进行加工处理，最后产生信息的系统</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1399, "rightCount": 1181, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4275, "type": "1", "stem": "<p>【模拟题】-信息系统的生命周期</p>\n<p>信息系统的生命周期可以简化为:系统规划、系统分析、系统设计、系统实施、系统运行和维护等阶段，其中与系统实施对应的软件生命周期阶段是(  )。</p>\n", "answer": [3], "analysis": "<p>P4，信息系统的生命周期可以简化为:系统规划(可行性分析与项目开发计划)，系统分析(需求分析)，系统设计(概要设计、详细设计)，系统实施(编码、测试)，系统运行和维护等阶段。</p>\n", "options": ["<p>项目开发计划</p>\n", "<p>需求分析</p>\n", "<p>详细设计</p>\n", "<p>测试</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1391, "rightCount": 951, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4276, "type": "1", "stem": "<p>【模拟题】-信息化内涵</p>\n<p>信息化内涵不包括(  )。</p>\n", "answer": [2], "analysis": "<p>P5,信息网络体系:包括信息资源、各种信息系统、公用通信网络平台等。信息产业基础:包括信息科学技术研究与开发、信息装备制造、信息咨询服务等。·社会运行环境:包括现代工农业、管理体制、政策法律、规章制度、文化教育、道德观念等生产关系与上层建筑。</p>\n<p>效用积累过程:包括劳动者素质、国家现代化水平和人民生活质量的不断提高，精神文明和物质文明建设不断进步等</p>\n", "options": ["<p>信息网络体系</p>\n", "<p>信息产业基础</p>\n", "<p>信息工具进步</p>\n", "<p>效用积累过程</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1398, "rightCount": 846, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4277, "type": "1", "stem": "<p>【模拟题】-&quot;十四五&quot;国家信息化规划不属于《&quot;十四五&quot;国家信息化规划》明确的内容是(  )。</p>\n", "answer": [1], "analysis": "<p>P5，《&quot;十四五&quot;国家信息化规划》明确了:建设泛在智联的数字基础设施体系，建立高效利用的数据要素资源体系，构建释放数字生产力的创新发展体系，培育先进安全的数字产业体系，构建产业数字化转型发展体系，构筑共建共治共享的数字社会治理体系，打造协同高效的数字政府服务体系，构建普惠便捷的数字民生保障体系，拓展互利共赢的数字领域国际合作体系和建立健全规范有序的数字化发展治理体系等重大任务。</p>\n", "options": ["<p>建设泛在智联的数字基础设施体系</p>\n", "<p>建立高效利用的技术要素资源体系</p>\n", "<p>构建释放数字生产力的创新发展体系</p>\n", "<p>培育先进安全的数字产业体系</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1394, "rightCount": 638, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 5, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4278, "type": "1", "stem": "<p>【2017年下半年-第3题】-信息系统的生命周期某大型种植企业今年要建设一个构建在公有云上的企业招投标信息系统,项目经理称现在正在进行软件采购，按照信息系统的生命周期5阶段划分方法，当前处于(  )</p>\n", "answer": [3], "analysis": "<p>P4，根据题意来看，已在进行软件采购，应是属于系统实施阶段，实施阶段涉及软硬件的购置。</p>\n<p>信息系统的生命周期可以简化为系统规划(可行性分析与项目开发计划)、系统分析(需求分析)、系统设计(概要设计、详细设计)、系统实施(编码、测试)、运行维护等5个阶段</p>\n<p>5阶段的生命周期:(划分即试行)</p>\n<p>①系统规划阶段:任务是对组织的环境、目标及现行系统的状况进行初步调查，根据组织目标和发展战略，确定信息系统的发展战略，对建设新系统的需求做出分析和预测，同时考虑建设新系统所受的各种约束，研究建设新系统的必要性和可能性。根据需要与可能，给出拟建系统的备选方案。对这些方案进行可行性研究，写出可行性研究报告。②系统分析阶段:任务是根据系统设计任务书所确定的范围，对现行系统进行详细调查，描述现行系统的业务流程，指出现行系统的局限性和不足之处，确定新系统的基本目标和逻辑功能要求，即提出新系统的逻辑模型。③系统设计阶段:简单地说，系统分析阶段的任务是回答系统&quot;做什么&quot;的问题，而系统设计阶段要回答的问题是&quot;怎么做&quot;。该阶段的任务是根据系统说明书中规定的功能要求，考虑实际条件，具体设计实现逻辑模型的技术方案，也就是设计新系统的物理模型。这个阶段又称为物理设计阶段，可分为总体设计(概要设计)和详细设计两个子阶段。这个阶段技术文档是系统设计说明书。</p>\n<p>(4)系统实施阶段:将设计的系统付诸实施的阶段。这一阶段的任务包括计算机等设备的购置安装和调试、程序的编写和调试、人员培训、数据文件转换、系统调试与转换等。这个阶段的特点是几个互相联系、互相制约的任务同时展开，必须精心安排、合理组织。系统实施是按实施计划分阶段完成的，每个阶段应写出实施进展报告。⑤系统运行和维护阶段:系统投入运行后，需要经常进行维护和评价，记录系统运行的情况根据一定的规则对系统进行必要的修改，评价系统的工作质量和经济效益。</p>\n", "options": ["<p>系统规划</p>\n", "<p>系统分析</p>\n", "<p>系统设计</p>\n", "<p>系统实施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1277, "rightCount": 1013, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4279, "type": "1", "stem": "<p>【2018年上半年-第3题】-信息系统的生命周期某快消品连锁企业委托科技公司A开发部署电子商务平台，A公司根据系统设计任务书所确定的范围，确定系统的基本目标和逻辑功能要求，提出新系统的逻辑模型，这属于信息系统生命周期中(  )阶段的工作。</p>\n", "answer": [1], "analysis": "<p>系统分析阶段的任务是根据系统设计任务书所确定的范围,对现行系统进行详细调查描述现行系统的业务流程，指出现行系统的局限性和不足之处，确定新系统的基本目标和逻辑功能要求，即提出新系统的逻辑模型。</p>\n", "options": ["<p>系统规划</p>\n", "<p>系统分析</p>\n", "<p>系统设计</p>\n", "<p>系统实施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1350, "rightCount": 582, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4280, "type": "1", "stem": "<p>【2018年下半年-第3题】-信息系统的生命周期(  )的任务是:根据系统说明书规定的功能要求，考虑实际条件，具体设计实现逻辑模型的技术方案。</p>\n", "answer": [2], "analysis": "<p>5阶段的生命周期:(划分即试行)</p>\n<p>①系统规划阶段:任务是对组织的环境、目标及现行系统的状况进行初步调查，根据组织目标和发展战略，确定信息系统的发展战略，对建设新系统的需求做出分析和预测，同时考虑建设新系统所受的各种约束，研究建设新系统的必要性和可能性。根据需要与可能，给出拟建系统的备选方案。对这些方案进行可行性研究，写出可行性研究报告。</p>\n<p>②系统分析阶段:任务是根据系统设计任务书所确定的范围，对现行系统进行详细调查，描述现行系统的业务流程，指出现行系统的局限性和不足之处，确定新系统的基本目标和逻辑功能要求，即提出新系统的逻辑模型。</p>\n<p>③系统设计阶段:简单地说，系统分析阶段的任务是回答系统&quot;做什么&quot;的问题，而系统设计阶段要回答的问题是&quot;怎么做&quot;。该阶段的任务是根据系统说明书中规定的功能要求，考虑实际条件，具体设计实现逻辑模型的技术方案，也就是设计新系统的物理模型。这个阶段又称为物理设计阶段，可分为总体设计(概要设计)和详细设计两个子阶段。这个阶段技术文档是系统设计说明书。</p>\n<p>④系统实施阶段:将设计的系统付诸实施的阶段。这一阶段的任务包括计算机等设备的购置、安装和调试、程序的编写和调试、人员培训、数据文件转换、系统调试与转换等。这个阶段的特点是几个互相联系、互相制约的任务同时展开，必须精心安排、合理组织。系统实施是按实施计划分阶段完成的，每个阶段应写出实施进展报告。⑤系统运行和维护阶段:系统投入运行后，需要经常进行维护和评价，记录系统运行的情况，根据一定的规则对系统进行必要的修改，评价系统的工作质量和经济效益。</p>\n", "options": ["<p>系统规划阶段</p>\n", "<p>系统分析阶段</p>\n", "<p>系统设计阶段</p>\n", "<p>系统实施阶段</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1218, "rightCount": 858, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4281, "type": "1", "stem": "<p>【2019年上半年-第3题】-信息系统的生命周期</p>\n<p>在信息系统的生命周期中，开发阶段不包括(  )。</p>\n", "answer": [0], "analysis": "<p>系统规划是立项阶段的。</p>\n", "options": ["<p>系统规划</p>\n", "<p>系统设计</p>\n", "<p>系统分析</p>\n", "<p>系统实施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1224, "rightCount": 782, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4282, "type": "1", "stem": "<p>【2022年上半年-第1题】-信息的特征</p>\n<p>为了表达一只小狗的信息，可以用汉字&quot;小狗&quot;，也可以通过一只小狗的彩色图片，还可以通过声音文件&quot;汪汪&quot;来表示。同一个信息可以借助不同的信息媒体表现出来。这体现了信息的(  )</p>\n", "answer": [1], "analysis": "<p>P3，传递性:信息在时间上的传递就是存储，在空间上的传递就是转移或扩散。依附性:信息的依附性可以从两个方面来理解，一方面，信息是客观世界的反映，任何信息必然由客观事物所产生，不存在无源的信息;另一方面，任何信息都要依附于一定的载体而存在，需要有物质的承担者，信息不能完全脱离物质而独立存在。及时性:指获得信息的时刻与事件发生时刻的间隔长短。动态性:信息是随着时间的变化而变化的。</p>\n", "options": ["<p>传递性</p>\n", "<p>依附性</p>\n", "<p>及时性</p>\n", "<p>动态性</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1183, "rightCount": 863, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4283, "type": "1", "stem": "<p>【2022年下半年-第1题】-信息系统生命周期在信息系统开发过程中，(  )阶段任务是回信息系统&quot;做什么&quot;的问题，(  )阶段是回答系统&quot;怎么做&quot;的问题。</p>\n", "answer": [1], "analysis": "<p>系统分析阶段的任务是回答系统&quot;做什么&quot;的问题，而系统设计阶段要回答的问题是&quot;怎么做&quot;</p>\n", "options": ["<p>规划、实施</p>\n", "<p>分析、设计</p>\n", "<p>设计、运行</p>\n", "<p>实施、运行</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1216, "rightCount": 857, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4284, "type": "1", "stem": "<p>【2024年上半年-第2批次】-信息系统生命周期关于信息系统生命周期的描述，不正确的是(  )。</p>\n", "answer": [2], "analysis": "<p>P4，信息系统的生命周期可以简化为:系统规划(可行性分析与项目开发计划)，系统分析(需求分析)，系统设计(概要设计、详细设计)，系统实施(编码、测试)，系统运行和维护等阶段。</p>\n", "options": ["<p>信息系统的产生、建设、运行、维护、完善构成一个循环的过程，并有一定的规律可循</p>\n", "<p>信息系统建设和维护随着各种环境变化，需要不断维护和修改，必要时可由新系统替代</p>\n", "<p>信息系统的生命周期可简化为系统规划、系统分析、系统设计、系统运行和维护等阶段</p>\n", "<p>信息系统建设周期长、投资大、用户习惯难以改变，定制化开发后无法进行重建和升级</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1219, "rightCount": 573, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4285, "type": "1", "stem": "<p>【教材练习】-新型基础设施</p>\n<p>支撑科学研究、技术开发、产品研制的具有公益属性的基础设施属于(  )。</p>\n", "answer": [2], "analysis": "<p>P7，新型基础设施主要包括如下三个方面。</p>\n<p>(1)信息基础设施。信息基础设施主要指基于新一代信息技术演化生成的基础设施。信息基础设施包括:①以5G、物联网、工业互联网、卫星互联网为代表的通信网络基础设施:②)以人工智能、云计算、区块链等为代表的新技术基础设施:③以数据中心、智能计算中心头代表的算力基础设施等。信息基础设施凸显&quot;技术新&quot;。</p>\n<p>(2)融合基础设施。融合基础设施主要指深度应用互联网、大数据、人工智能等技术，支撑传统基础设施转型升级,进而形成的融合基础设施。融合基础设施包括智能交通基础设施</p>\n<p>智慧能源基础设施等。融合基础设施重在&quot;应用新&quot;(3)创新基础设施。创新基础设施主要指支撑科学研究、技术开发、产品研制的具有公益属性的基础设施。创新基础设施包括重大科技基础设施、科教基础设施、产业技术创新基础设施等。创新基础设施强调&quot;平台新&quot;</p>\n", "options": ["<p>信息基础设施</p>\n", "<p>融合基础设施</p>\n", "<p>创新基础设施</p>\n", "<p>网络基础设施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1131, "rightCount": 733, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4286, "type": "1", "stem": "<p>【模拟题】-新型基础设施</p>\n<p>下列不属于&quot;新型基建的方向&quot;的是(  )</p>\n", "answer": [3], "analysis": "<p>P7,&quot;新型基础设施建设&quot;其主要包括5G基建、特高压、城际高速铁路和城际轨道交通、新能源汽车充电桩、大数据中心、人工智能、工业互联网等七大领域。</p>\n", "options": ["<p>新能源汽车充电桩</p>\n", "<p>工业互联网</p>\n", "<p>特高压</p>\n", "<p>移动互联网</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1109, "rightCount": 906, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4287, "type": "1", "stem": "<p>【模拟题】-新型基础设施</p>\n<p>下列不属于&quot;新型基础设施3个方面&quot;的是(  )</p>\n", "answer": [3], "analysis": "<p>P7，新型基础设施主要包括如下三个方面。</p>\n<p>(1)信息基础设施。信息基础设施主要指基于新一代信息技术演化生成的基础设施。信息基础设施包括:①以5G、物联网、工业互联网、卫星互联网为代表的通信网络基础设施;②)以人工智能、云计算、区块链等为代表的新技术基础设施;③以数据中心、智能计算中心为代表的算力基础设施等。信息基础设施凸显&quot;技术新&quot;。</p>\n<p>(2)融合基础设施。融合基础设施主要指深度应用互联网、大数据、人工智能等技术,支撑传统基础设施转型升级,进而形成的融合基础设施。融合基础设施包括智能交通基础设施智慧能源基础设施等。融合基础设施重在&quot;应用新&quot;</p>\n<p>(3)创新基础设施。创新基础设施主要指支撑科学研究、技术开发、产品研制的具有公益属性的基础设施。创新基础设施包括重大科技基础设施、科教基础设施、产业技术创新基础设施等。创新基础设施强调&quot;平台新&quot;。</p>\n", "options": ["<p>信息基础设施</p>\n", "<p>融合基础设施</p>\n", "<p>创新基础设施</p>\n", "<p>工业基础设施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1090, "rightCount": 1007, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4288, "type": "1", "stem": "<p>【模拟题】-新型基础设施下列不属于信息基础设施的是(  )。</p>\n", "answer": [3], "analysis": "<p>P7，新型基础设施主要包括如下三个方面。(1)信息基础设施。信息基础设施主要指基于新一代信息技术演化生成的基础设施。信息基础设施包括:①以5G、物联网、工业互联网、卫星互联网为代表的通信网络基础设施;②以人工智能、云计算、又块链等为代表的新技术基础设施:③以数据中心、智能计算中心为代表的算力基础设施等。信息基础设施凸显&quot;技术新&quot;D选项重大科技基础设施属于创新基础设施。</p>\n", "options": ["<p>通信网络基础设施</p>\n", "<p>新技术基础设施</p>\n", "<p>算力基础设施</p>\n", "<p>重大科技基础设施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1104, "rightCount": 853, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4289, "type": "1", "stem": "<p>【模拟题】-新型基础设施融合基础设施包括(  )。</p>\n<p>①智能交通基础设施 ②算力基础设施 ③科教基础设施 ④智慧能源基础设施</p>\n", "answer": [2], "analysis": "<p>P7，(2)融合基础设施。融合基础设施主要指深度应用互联网、大数据、人工智能等技术，支撑传统基础设施转型升级，进而形成的融合基础设施。融合基础设施包括智能交通基础设施、智慧能源基础设施等。融合基础设施重在&quot;应用新&quot;。</p>\n", "options": ["<p>①②</p>\n", "<p>②③</p>\n", "<p>① ④</p>\n", "<p>② ④</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1092, "rightCount": 810, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4290, "type": "1", "stem": "<p>【教材练习】-工业互联网</p>\n<p>工业互联网的体系不包括(  )。</p>\n", "answer": [2], "analysis": "<p>P9，工业互联网平台体系具有四大层级:它以网络为基础，平台为中枢，数据为要素安全为保障。</p>\n", "options": ["<p>网络</p>\n", "<p>平台</p>\n", "<p>技术</p>\n", "<p>安全</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1109, "rightCount": 711, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4291, "type": "1", "stem": "<p>【模拟题】-工业互联网</p>\n<p>(  )它既是工业数字化、网络化、智能化转型的基础设施，也是互联网、大数据、人工智能与实体经济深度融合的应用模式，同时也是一种新业态、新产业，将重塑企业形态、供应链和产业链。</p>\n", "answer": [0], "analysis": "<p>P8，工业互联网不是互联网在工业的简单应用，是具有更为丰富的内涵和外延。它既是工业数字化、网络化、智能化转型的基础设施，也是互联网、大数据、人工智能与实体经济深度融合的应用模式，同时也是一种新业态、新产业，将重塑企业形态、供应链和产业链。</p>\n", "options": ["<p>工业互联网</p>\n", "<p>商业互联网</p>\n", "<p>农业互联网</p>\n", "<p>经济互联网</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1031, "rightCount": 1001, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4292, "type": "1", "stem": "<p>【模拟题】-工业互联网关于工业互联网平台体系具有四大层级正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P9，工业互联网平台体系具有四大层级:它以网络为基础，平台为中枢，数据为要素安全为保障。</p>\n", "options": ["<p>网络为基础</p>\n", "<p>平台为要素</p>\n", "<p>数据为中枢</p>\n", "<p>网络为保障</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1075, "rightCount": 876, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4293, "type": "1", "stem": "<p>【模拟题】-工业互联网</p>\n<p>以下关于工业互联网的描述正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P8-10，工业互联网平台体系包括边缘层、IaaS、 PaaS 和Saas\n四个层级平台化设计是依托工业互联网平台，汇聚人员、算法、模型、任务等设计资源，实现高水平高效率的轻量化设计、并行设计、敏捷设计、交互设计和基于模型的设计，变革传统设计方式，提升研发质量和效率。</p>\n<p>工业互联网不是互联网在工业的简单应用，是具有更为丰富的内涵和外延。</p>\n", "options": ["<p>工业互联网平台体系包括 IaaS、PaaS 和 SaaS 三个层级</p>\n", "<p>智能化制造是依托工业互联网平台，汇聚人员、算法、模型、任务等设计资源</p>\n", "<p>工业互联网是互联网在工业的简单应用</p>\n", "<p>工业互联网是新一代信息通信技术与工业经济深度融合的新型基础设施</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1125, "rightCount": 751, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4294, "type": "1", "stem": "<p>【模拟题】-车联网</p>\n<p>以下关于车联网的描述正确的是(  )。</p>\n", "answer": [3], "analysis": "<p>P11，车联网系统是一个&quot;端、管、云&quot;三层体系。</p>\n<p>(1)端系统是汽车的智能传感器负责采集与获取车辆的智能信息，感知行车状态与环境;是具有车内通信、车间通信、车网通信的泛在通信终端;同时还是让汽车具备1oV\n寻址和网络可信标识等能力的设备。</p>\n<p>(2)管系统解决车与车、车与路、车与网、车与人等的互联互通，实现车辆自组网及多种异构网络之间的通信与漫游，在功能和性能上保障实时性、可服务性与网络泛在性，同时它是公网与专网的统一体。</p>\n<p>(3)云系统。车联网是一个云架构的车辆运行信息平台，它的生态链包含了ITS、物流、客货运、危特车辆、汽修汽配、汽车租赁、企事业车辆管理、汽车制造商、4S\n店、车管、保险.紧急救援、移动互联网等，是多源海量信息的汇聚，因此需要虚拟化、安全认证、实时交互、海量存储等云计算功能，其应用系统也是围绕车辆的数据汇聚、计算、调度、监控、管理与应用的复合体系。</p>\n", "options": ["<p>车联网系统是一个&quot;端、管、云、车&quot;四层体系</p>\n", "<p>云系统是汽车的智能传感器负责采集与获取车辆的智能信息，感知行车状态与环境</p>\n", "<p>端系统解决车与车、车与路、车与网、车与人等的互联互通</p>\n", "<p>云系统是一个云架构的车辆运行信息平台</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1107, "rightCount": 679, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4295, "type": "1", "stem": "<p>【模拟题】-车联网车联网分别是车与云平台、车与车、车与路、(  )、车内设备之间等全方位网络链接。</p>\n", "answer": [0], "analysis": "<p>P11，车联网分别是车与云平台、车与车、车与路、车与人、车内设备之间等全方位网络链接。</p>\n", "options": ["<p>车与人</p>\n", "<p>车与网络</p>\n", "<p>车与传感器</p>\n", "<p>车与服务</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1022, "rightCount": 912, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4296, "type": "1", "stem": "<p>【模拟题】-车联网</p>\n<p>以下关于车联网的描述正确的是(  )。</p>\n", "answer": [0], "analysis": "<p>P11，车与车间的通信是指车辆与车辆之间实现信息交流与信息共享，包括车辆位置、行驶速度等车辆状态信息，可用于判断道路车流状况。所以B错。</p>\n<p>车与路间的通信是指借助地面道路固定通信设施实现车辆与道路间的信息交流,用于监测道路路面状况，引导车辆选择最佳行驶路径。所以C错。</p>\n<p>车与人间的通信是指用户可以通过\nWi-Fi、蓝牙、蜂窝等无线通信手段与车辆进行信息沟通，使用户能通过对应的移动终端设备监测并控制车辆。所以D错。</p>\n", "options": ["<p>车与云平台间的通信是指车辆通过卫星无线通信或移动蜂窝等无线通信技术实现与车联网服务平台的信息传输，接收平台下达的控制指令，实时共享车辆数据。</p>\n", "<p>车与云平台间的通信是指车辆与车辆之间实现信息交流与信息共享,包括车辆位置、行驶速度等车辆状态信息，可用于判断道路车流状况。</p>\n", "<p>车与人间的通信是指借助地面道路固定通信设施实现车辆与道路间的信息交流,用于监测道路路面状况，引导车辆选择最佳行驶路径。</p>\n", "<p>车与车间的通信是指用户可以通过\nWi-Fi、蓝牙、蜂窝等无线通信手段与车辆进行信息沟通,使用户能通过对应的移动终端设备监测并控制车辆。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1037, "rightCount": 802, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4297, "type": "1", "stem": "<p>【2023年上半年-第1题】-新型基础设施&quot;新型基础设施&quot;主要包括信息技术设施，融合基础设施和创新基础设施三个方面，其中信息基础设施包括(  )。</p>\n<p>①通信基础设施 ②智能交通基础设施③新技术基础设施 ④科硏基础设施\n⑤算力基础设施</p>\n", "answer": [0], "analysis": "<p>P7，新型基础设施主要包括如下三个方面。(1)信息基础设施。信息基础设施主要指基于新一代信息技术演化生成的基础设施。信息基础设施包括:①以\n5G、物联网、工业互联网、卫星互联网为代表的通信网络基础设施;②以人工智能、云计算、区块链等为代表的新技术基础设施:③以数据中心、智能计算中心为代表的算力基础设施等。信息基础设施凸显&quot;技术新&quot;</p>\n<p>(2)融合基础设施。融合基础设施主要指深度应用互联网、大数据、人工智能等技术，支撑传统基础设施转型升级，进而形成的融合基础设施。融合基础设施包括智能交通基础设施、智慧能源基础设施等。融合基础设施重在&quot;应用新&quot;。(3)创新基础设施。创新基础设施主要指支撑科学研究、技术开发、产品研制的具有公益属性的基础设施。创新基础设施包括重大科技基础设施、科教基础设施、产业技术创新基础设施等。创新基础设施强调&quot;平台新&quot;</p>\n", "options": ["<p>①③⑤</p>\n", "<p>①④⑤</p>\n", "<p>②③④</p>\n", "<p>②③⑤</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 995, "rightCount": 861, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4298, "type": "1", "stem": "<p>【2023年下半年-第2批次自编】-车联网以下关于车联网的说法中，错误的是(  )</p>\n", "answer": [3], "analysis": "<p>P11，车联网(IoV)系统是一个&quot;端、管、云&quot;三层体系。</p>\n<p>(1)端系统。端系统是汽车的智能传感器负责采集与获取车辆的智能信息，感知行车状态与环境:是具有车内通信、车间通信、车网通信的泛在通信终端:同时还是让汽车具备IoV\n寻址</p>\n<p>和网络可信标识等能力的设备。</p>\n<p>(2)管系统。管系统解决车与车、车与路、车与网、车与人等的互联互通，实现车辆自组网及多种异构网络之间的通信与漫游，在功能和性能上保障实时性、可服务性与网络泛在性，同时它是公网与专网的统一体。</p>\n<p>(3)云系统。车联网是一个云架构的车辆运行信息平台，它的生态链包含了ITS、物流、客货运、危特车辆、汽修汽配、汽车租赁、企事业车辆管理、汽车制造商、4s店、车管、保险、紧急救援、移动互联网等，是多源海量信息的汇聚，因此需要虚拟化、安全认证、实时交瓦、海量存储等云计算功能，其应用系统也是围绕车辆的数据汇聚、计算、调度、监控、管理与应用的复合体系。</p>\n", "options": ["<p>车联网(IoV)系统是一个&quot;端、管、云&quot;三层体系</p>\n", "<p>管系统解决车与车、车与路、车与网、车与人等的互联互通，实现车辆自组网及多种异构网络之间的通信与漫游，在功能和性能上保障实时性、可服务性与网络泛在性，同时它是公网与专网的统一体</p>\n", "<p>车与云平台间的通信是指车辆通过卫星无线通信或移动蜂窝等无线通信技术实现与车联网服务平台的信息传输，接收平台下达的控制指令，实时共享车辆数据</p>\n", "<p>云系统是汽车的智能传感器负责采集与获取车辆的智能信息,感知行车状态与环境;是具有车内通信、车间通信、车网通信的泛在通信终端;同时还是让汽车具备IoV\n寻址和网络可信标识等能力的设备。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1022, "rightCount": 796, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4299, "type": "1", "stem": "<p>【2023年下半年-第4批次】-车联网</p>\n<p>车联网系统是一个&quot;端、管、云&quot;三层体系。其中(  )解决互联互通问题，(  )是多源海量信息的汇聚。</p>\n", "answer": [3], "analysis": "<p>P11。车联网(Internetof\nVehicles,IoV)系统是一个&quot;端、管、云&quot;三层体系。</p>\n<p>(1)端系统。端系统是汽车的智能传感器负责采集与获取车辆的智能信息，感知行车状态与环境:是具有车内通信、车间通信、车网通信的泛在通信终端:同时还是让汽车具备IoV寻址</p>\n<p>和网络可信标识等能力的设备。</p>\n<p>(2)管系统。管系统解决车与车、车与路、车与网、车与人等的互联互通，实现车辆自组网及多种异构网络之间的通信与漫游，在功能和性能上保障实时性、可服务性与网络泛在性，同时它是公网与专网的统一体。</p>\n<p>(3)云系统。车联网是一个云架构的车辆运行信息平台，它的生态链包含了ITS、物流、客货运、危特车辆、汽修汽配、汽车租赁、企事业车辆管理、汽车制造商、4S店、车管、保险、紧急救援、移动互联网等，是多源海量信息的汇聚，因此需要虚拟化、安全认证、实时交互、海量存储等云计算功能，其应用系统也是围绕车辆的数据汇聚、计算、调度、监控、管理与应用的复合体系。</p>\n", "options": ["<p>云系统、端系统</p>\n", "<p>端系统、云系统</p>\n", "<p>端系统、管系统</p>\n", "<p>管系统、云系统</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1025, "rightCount": 791, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4300, "type": "1", "stem": "<p>【2024年上半年-第1批次】-新型基础设施建设新型基础设施建设是以新发展理念为引领，以(  )为驱动，以信息网络为基础，面向高质量发展需要的基础设施体系。</p>\n", "answer": [1], "analysis": "<p>P7,新型基础设施是以新发展理念为引领，以技术创新为驱动，以信息网络为基础面向高质量发展需要，提供数字转型、智能升级、融合创新等服务的基础设施体系。目前，新型基础设施主要包括如下三个方面:信息基础设施、融合基础设施、创新基础设施。</p>\n", "options": ["<p>工业互联网</p>\n", "<p>技术创新</p>\n", "<p>人工智能</p>\n", "<p>区块链</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1016, "rightCount": 809, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4301, "type": "1", "stem": "<p>【2024年上半年-第2批次】-车联网</p>\n<p>(  )不属于车联网网络连接范畴。</p>\n", "answer": [2], "analysis": "<p>P11，车联网分别是车与云平台、车与车、车与路、车与人、车内设备之间等全方位网络链接。(1)车与云平台间的通信是指车辆通过卫星无线通信或移动蜂窝等无线通信技术实现与车联网服务平台的信息传输，接收平台下达的控制指令，实时共享车辆数据。</p>\n<p>(2)车与车间的通信是指车辆与车辆之间实现信息交流与信息共享，包括车辆位置、行驶速度等车辆状态信息，可用于判断道路车流状况。</p>\n<p>(3)车与路间的通信是指借助地面道路固定通信设施实现车辆与道路间的信息交流，用于监测道路路面状况，引导车辆选择最佳行驶路径。</p>\n<p>(4)车与人间的通信是指用户可以通过wi-Fi、蓝牙、蜂窝等无线通信手段与车辆进行信息沟通，使用户能通过对应的移动终端设备监测并控制车辆。</p>\n<p>(5)车内设备间的通信是指车辆内部各设备间的信息数据传输，用于对设备状态的实时检测与运行控制，建立数字化的车内控制系统。</p>\n", "options": ["<p>通过无线通信技术实现与服务平台的信息传输</p>\n", "<p>人通过运营商移动网络与车辆之间进行用于控制车辆的信息沟通</p>\n", "<p>人与人之间在车上通过运营商的移动网络进行通话与短信沟通。</p>\n", "<p>车内设备之间进行用于对设备状态实时监测的信息数据传输</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 988, "rightCount": 837, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4302, "type": "1", "stem": "<p>【模拟题】-农业农村现代化实现(  )是全面建设社会主义现代化国家的重大任务，需要将先进技术、现代装备、管理理念等引入农业，将基础设施和基本公共服务向农村延伸覆盖，提高农业生产效率，改善乡村面貌，提升农民生活品质，促进农业全面升级、农村全面进步、农民全面发展。</p>\n", "answer": [0], "analysis": "<p>P12，实现农业农村现代化是全面建设社会主义现代化国家的重大任务，需要将先进技术、现代装备、管理理念等引入农业，将基础设施和基本公共服务向农村延伸覆盖，提高农业生产效率，改善乡村面貌，提升农民生活品质，促进农业全面升级、农村全面进步、农民全面发展。</p>\n", "options": ["<p>农业农村现代化</p>\n", "<p>工业化</p>\n", "<p>信息化</p>\n", "<p>城镇化</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 983, "rightCount": 885, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4303, "type": "1", "stem": "<p>【模拟题】-信息化与工业化</p>\n<p>信息化与工业化主要是在技术、产品、业务、(  )四个方面进行融合</p>\n", "answer": [2], "analysis": "<p>P13，信息化与工业化主要在技术、产品、业务、产业四个方面进行融合</p>\n", "options": ["<p>工业</p>\n", "<p>服务业</p>\n", "<p>产业</p>\n", "<p>制造业</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1017, "rightCount": 756, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 3, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4304, "type": "1", "stem": "<p>【模拟题】-信息化与工业化</p>\n<p>(  )是指工业技术与信息技术的融合，产生新的技术，推动技术创新。例如，汽车制造技术和电子技术融合产生的汽车电子技术;工业和计算机控制技术融合产生的工业控制技术。</p>\n", "answer": [1], "analysis": "<p>P13，技术融合是指工业技术与信息技术的融合，产生新的技术，推动技术创新。例如，汽车制造技术和电子技术融合产生的汽车电子技术:工业和计算机控制技术融合产生的工业控制技术。</p>\n", "options": ["<p>业务融合</p>\n", "<p>技术融合</p>\n", "<p>产业衍生</p>\n", "<p>产品融合</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 990, "rightCount": 765, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4305, "type": "1", "stem": "<p>【模拟题】-信息化与工业化</p>\n<p>(  )是指电子信息技术或产品渗透到产品中，增加产品的技术含量。</p>\n", "answer": [3], "analysis": "<p>P13，信息化与工业化主要在技术、产品、业务、产业四个方面进行融合。(1)技术融合。技术融合是指工业技术与信息技术的融合，产生新的技术，推动技术创新。例如，汽车制造技术和电子技术融合产生的汽车电子技术;工业和计算机控制技术融合产生的工业控制技术。</p>\n<p>(2)产品融合。产品融合是指电子信息技术或产品渗透到产品中,增加产品的技术含量。例如，普通机床加上数控系统之后就变成了数控机床;传统家电采用了智能化技术之后就变成了智能家电;普通飞机模型增加控制芯片之后就成了遥控飞机。信息技术含量的提高使产品的附加值大大提高。</p>\n<p>(3)业务融合。业务融合是指信息技术应用到企业研发设计、生产制造、经营管理、市场营销等各个环节，推动企业业务创新和管理升级。例如，计算机管理方式改变了传统手工台账，极大地提高了管理效率;信息技术应用提高了生产自动化、智能化程度，生产效率大大提高:网络营销成为一种新的市场营销方式，受众大量增加，营销成本大大降低。</p>\n<p>(4)产业街生。产业街生是指两化融合可以催生出的新产业,形成一些新兴业态,如工业电子工业软件、工业信息服务业。工业电子包括机械电子、汽车电子、船舶电子、航空电子等;工业软件包括工业设计软件、工业控制软件等;工业信息服务业包括工业企业B2B电子商务、工业原材料或产成品大宗交易、工业企业信息化咨询等。</p>\n", "options": ["<p>业务融合</p>\n", "<p>技术融合</p>\n", "<p>产业衍生</p>\n", "<p>产品融合</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 988, "rightCount": 731, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4306, "type": "1", "stem": "<p>【模拟题】-智能制造智能制造的建设是一项持续性的系统工程，涵盖企业的方方面面。能力要素提出了智能制造能力成熟度等级提升的关键方面，包括人员、技术、资源和(  )。</p>\n", "answer": [3], "analysis": "<p>P15，能力要素提出了智能制造能力成熟度等级提升的关键方面，包括人员、技术、资源和制造。人员包括组织战略、人员技能2个能力域。技术包括数据、集成和信息安全3个能力域。资源包括装备、网络2个能力域。制造包括设计、生产、物流、销售和服务5个能力域。设计包括产品设计和工艺设计2个能力子域;生产包括采购、计划与调度、生产作业、设备管理、安全环保、仓储配送、能源管理7个能力子域;物流包括物流1个能力子域;销售包括销售1个能力子域:服务包括客户服务和产品服务2个能力子域。</p>\n", "options": ["<p>设计</p>\n", "<p>生产</p>\n", "<p>物流</p>\n", "<p>制造</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1008, "rightCount": 700, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4307, "type": "1", "stem": "<p>【模拟题】-智能制造能力成熟度模型GB/T39116《智能制造能力成熟度模型》规定了企业智能制造能力在不同阶段应达到的水平。企业应开始对实施智能制造的基础和条件进行规划，能够对核心业务活动(设计、生产、物流、销售、服务)进行流程化管理。</p>\n", "answer": [0], "analysis": "<p>P15，一级(规划级):企业应开始对实施智能制造的基础和条件进行规划，能够对核心业务活动(设计、生产、物流、销售、服务)进行流程化管理。·二级(规范级):企业应采用自动化技术、信息技术手段对核心装备和业务活动等进行改造和规范，实现单一业务活动的数据共享。三级(集成级):企业应对装备、系统等开展集成，实现跨业务活动间的数据共享。·四级(优化级):企业应对人员、资源、制造等进行数据挖掘，形成知识、型等，实现对核心业务活动的精准预测和优化。·五级(引领级):企业应基于模型持续驱动业务活动的优化和创新，实现产业链协同并衍生新的制造模式和商业模式。</p>\n", "options": ["<p>一级(规划级)</p>\n", "<p>二级(规范级)</p>\n", "<p>三级(集成级)</p>\n", "<p>四级(优化级)</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1043, "rightCount": 631, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4308, "type": "1", "stem": "<p>【教材练习】-智能制造能力成熟度模型GB/T39116《智能制造能力成熟度模型》规定了企业智能制造能力在不同阶段应达到的水平。若企业应对装备、系统等开展集成，实现跨业务活动间的数据共享，则该企业属于(  )水平。</p>\n", "answer": [2], "analysis": "<p>·一级(规划级):企业应开始对实施智能制造的基础和条件进行规划，能够对核心业务活动(设计、生产、\n物流、销售、服务)进行流程化管理。·二级(规范级):企业应采用自动化技术、信息技术手段对核心装备和业务活动等进行改造和规范，实现单一业务活动的数据共享。·三级(集成级):企业应对装备、系统等开展集成，实现跨业务活动间的数据共享。·四级(优化级):企业应对人员、资源、制造等进行数据挖掘，形成知识、模型等，实</p>\n<p>现对核心业务活动的精准预测和优化。·五级(引领级):企业应基于模型持续驱动业务活动的优化和创新，实现产业链协同并衍生新的制造模式和商业模式。</p>\n", "options": ["<p>一级(规划级)</p>\n", "<p>二级(规范级)</p>\n", "<p>三级(集成级)</p>\n", "<p>四级(优化级)</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 938, "rightCount": 870, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4309, "type": "1", "stem": "<p>【模拟题】-消费互联网消费互联网具有的属性包括(  )</p>\n<p>①媒体属性 ②产业属性 ③消费属性④ 服务属性</p>\n", "answer": [0], "analysis": "<p>P16，消费互联网具有的属性包括:</p>\n<p>媒体属性:由自媒体、社会媒体以及资讯为主的门户网站。</p>\n<p>产业属性:由在线旅行和为消费者提供生活服务的电子商务等其他组成。</p>\n", "options": ["<p>①②</p>\n", "<p>①③</p>\n", "<p>②③</p>\n", "<p>③④</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1060, "rightCount": 464, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4310, "type": "1", "stem": "<p>【模拟题】-消费互联网</p>\n<p>以下关于消费互联网的描述不正确的是(  )。</p>\n", "answer": [2], "analysis": "<p>P16，消费互联网不仅仅给人们带来了生活方式的变化和生活质量的提高，而且推动了社会生活的深层变革，那就是&quot;无身份社会&quot;的建立。</p>\n", "options": ["<p>消费互联网是以个人为用户，以日常生活为应用场景的应用形式，满足消费者在互联网中的消费需求而生的互联网类型</p>\n", "<p>消费互联网本质是个人虚拟化，增强个人生活消费体验</p>\n", "<p>消费互联网不仅仅给人们带来了生活方式的变化和生活质量的提高，而且推动了社会生活的深层变革，那就是&quot;有身份社会&quot;的建立。</p>\n", "<p>消费互联网以消费者为服务中心，针对个人用户提升消费过程的体验</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1031, "rightCount": 651, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4311, "type": "1", "stem": "<p>【2021年上半年-第5题】-&quot;十四五&quot;规划</p>\n<p>根据&quot;十四五&quot;规划和2035年远景目标纲要，到2035年，我国进入创新型国家前列，基本实现新型工业化、信息化、城镇化、(  )。</p>\n", "answer": [0], "analysis": "<p>属于时政题，《&quot;十四五&quot;规划和 2035年远景目标纲要》提出:展望\n2035年我国将进入创新型国家前列，基本实现新型工业化、信息化、城镇化、农业现代化，建成现代化经济体系。</p>\n", "options": ["<p>农业现代化</p>\n", "<p>区域一体化</p>\n", "<p>智能化</p>\n", "<p>数字化</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 963, "rightCount": 731, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4312, "type": "1", "stem": "<p>【2023年下半年-第3批次自编】-智能制造能力成熟度模型</p>\n<p>GB/T39116《智能制造能力成熟度模型》规定了企业智能制造能力在不同阶段应达到的水平。企业应对人员、资源、制造等进行数据挖掘，形成知识、模型等，实现对核心业务活动的精准预测和优化。</p>\n", "answer": [2], "analysis": "<p>P15，·一级(规划级):企业应开始对实施智能制造的基础和条件进行规划，能够对核心业务活动(设计、生产、物流、销售、服务)进行流程化管理。二级(规范级):企业应采用自动化技术、信息技术手段对核心装备和业务活动等进行改造和规范，实现单一业务活动的数据共享。·三级(集成级):企业应对装备、系统等开展集成，实现跨业务活动间的数据共享·四级(优化级):企业应对人员、资源、制造等进行数据挖掘，形成知识、型等，实现对核心业务活动的精准预测和优化。五级(引领级):企业应基于模型持续驱动业务活动的优化和创新，实现产业链协同并衍生新的制造模式和商业模式。</p>\n", "options": ["<p>二级(规范级)</p>\n", "<p>三级(集成级)</p>\n", "<p>四级(优化级)</p>\n", "<p>五级(引领级)</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 923, "rightCount": 833, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4313, "type": "1", "stem": "<p>【2024年上半年-第1批次】-两化融合</p>\n<p>两化融合是信息化和工业化的高层次的深度结合，主要在技术、产品、(  )、产业四个方面进行融合。</p>\n", "answer": [2], "analysis": "<p>P13，信息化与工业化主要在技术、产品、业务、产业四个方面进行融合。</p>\n", "options": ["<p>硬件</p>\n", "<p>软件</p>\n", "<p>业务</p>\n", "<p>网络</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 931, "rightCount": 846, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4314, "type": "1", "stem": "<p>【教材练习】-数字中国</p>\n<p>《中华人民共和国国民经济和社会发展第十四个五年规划和2035年远景目标纲要》中从数字经济、数字政府、数字社会、(  )四个维度出发勾勒了建设数字中国的宏伟蓝图。</p>\n", "answer": [0], "analysis": "<p>P17，十四五规划原文，加快建设数字经济、数字社会、数字政府，以数字化转型整体驱动生产方式、生活方式和治理方式变革。营造良好数字生态，为加快建设数字经济、数字社会、数字政府提供良好环境和有力支撑。所以包含数字经济、数字政府、数字社会和数字生态。</p>\n", "options": ["<p>数字生态</p>\n", "<p>数字技术</p>\n", "<p>数字服务</p>\n", "<p>数字人才</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 957, "rightCount": 788, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4315, "type": "1", "stem": "<p>【教材练习】-智慧城市</p>\n<p>(  )不属于智慧城市核心能力要素。</p>\n", "answer": [2], "analysis": "<p>P24，重点强化数据治理、数字孪生、边际决策、多元融合和态势感知五个核心能力要素建设。</p>\n", "options": ["<p>数据治理、边际决策、多元融合</p>\n", "<p>数据治理、数字李生、边际决策</p>\n", "<p>数据管理、数字孪生、态势感知</p>\n", "<p>数字孪生、多元融合、态势感知</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 969, "rightCount": 690, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4316, "type": "1", "stem": "<p>【教材练习】-智慧城市</p>\n<p>智慧城市发展过程中，能够明确智慧城市发展战略、原则、目标和实施计划等，推进城市基础设施的智能化改造，多领域实现信息系统单项应用，对智慧城市全生命周期实施管理，则该智慧城市成熟度处于(  )水平。</p>\n", "answer": [1], "analysis": "<p>P25，智慧城市发展成熟度划分为规划级、管理级、协同级、优化级、引领级5个等级。</p>\n<p>一级(规划级);应围绕智慧城市的发展进行策划，明确相关职责分工和工作机制等，初步开展数据采集和应用，确保相关活动有序开展。</p>\n<p>二级(管理级);应明确智慧城市发展战略、原则、目标和实施计划等，推进城市基础设施智能化改造，多领域实现信息系统单向应用，对智慧城市全生命周期实施管理。</p>\n<p>三级(协同级);应管控智慧城市各项发展目标，实施多业务、多层级、跨领域应用系统的集成，持续推进信息资源的共享与交换，推动惠民服务、城市治理、生态宜居、产业发展等的融合创新，实现跨领域的协同改进。</p>\n<p>四级(优化级);应聚焦智慧城市与城市经济社会发展深度融合，基于数据与知识模型实施城市经济、社会精准化治理，推动数据要素的价值挖掘和开发利用，推进城市竞争力持续提升。</p>\n<p>五级(引领级):应构建智慧城市敏捷发展能力，实现城市物理空间、社会空间、信息空间的融合演进和共生共治，引领城市集群治理联动，形成高质量发展共同体。</p>\n", "options": ["<p>规划级</p>\n", "<p>管理级</p>\n", "<p>协同级</p>\n", "<p>优化级</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 945, "rightCount": 708, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4317, "type": "1", "stem": "<p>【模拟题】-数字经济</p>\n<p>以下说法中不正确的是(  )。</p>\n", "answer": [], "analysis": "<p>P18，数字产业化是数字经济的基础部分。</p>\n", "options": ["<p>数字经济包括数字产业化、产业数字化、数字化治理和数据价值化四个部分。</p>\n", "<p>数据价值化是数字经济的基础部分</p>\n", "<p>数字产业化发展重点包括:云计算，大数据，物联网，工业互联网，区块链，人工智能，虚拟现实和增强现实</p>\n", "<p>《中华人民共和国国民经济和社会发展第十四个五年规划和2035年远景目标纲要》明确提出了推进产业数字化转型，实施&quot;上云用数赋智&quot;行动，推动数据赋能全产业链协同转型。答案:B</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1138, "rightCount": 0, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4318, "type": "1", "stem": "<p>【模拟题】-数字化治理</p>\n<p>(  )是数字经济的组成部分之一，包括但不限于多元治理，以&quot;信息技术+治理&quot;为典型特征的技管结合，以及数字化公共服务等。</p>\n", "answer": [3], "analysis": "<p>P19，数字化治理通常指依托互联网、大数据、人工智能等技术和应用，创新社会治理方法与手段，优化社会治理模式，推进社会治理的科学化、精细化、高效化，助力社会治理现代化。数字化治理是数字经济的组成部分之一，包括但不限于多元治理，以&quot;信息技术+治理&quot;为典型特征的技管结合，以及数字化公共服务等。</p>\n", "options": ["<p>产业智能化</p>\n", "<p>产业数字化</p>\n", "<p>数字云</p>\n", "<p>数字化治理</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 962, "rightCount": 757, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4319, "type": "1", "stem": "<p>【模拟题】-数据价值化</p>\n<p>数据资源化、数据资产化、数据资本化，三个要素构成(  )的&quot;三化&quot;框架。</p>\n", "answer": [2], "analysis": "<p>P20，数据价值化是指以数据资源化为起点，经历数据资产化、数据资本化阶段，实现数据价值化的经济过程。上述三个要素构成数据价值化的&quot;三化&quot;框架，即数据资源化、数据资产化、数据资本化。</p>\n", "options": ["<p>数字化治理</p>\n", "<p>产业智能化</p>\n", "<p>数据价值化</p>\n", "<p>产业数字化</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 952, "rightCount": 720, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4320, "type": "1", "stem": "<p>【模拟题】-数据价值化以下关于数据价值化的&quot;三化&quot;框架，说法正确的是(  )。</p>\n", "answer": [2], "analysis": "<p>P20，数据价值化是指以数据资源化为起点，经历数据资产化、数据资本化阶段，实现数据价值化的经济过程。上述三个要素构成数据价值化的&quot;三化&quot;框架，即数据资源化、数据资产化、数据资本化，细化描述为:·数据资源化:是使无序、混乱的原始数据成为有序、有使用价值的数据资源。数据资源化阶段包括通过数据采集、整理、聚合、分析等，形成可采、可见、标准、互通、可信的高质量数据资源。数据资源化是激发数据价值的基础，其本质是提升数据质量，形成数据使用价值的过程。</p>\n<p>·数据资产化:是数据通过流通交易给使用者或者所有者带来的经济利益的过程。数据资产化是实现数据价值的核心，其本质是形成数据交换价值，初步实现数据价值的过程。数据资本化:主要包括两种方式，数据信贷融资与数据证券化。数据资本化是拓展数据价值的途径，其本质是实现数据要素社会化配置。</p>\n", "options": ["<p>数据资产化:是使无序、混乱的原始数据成为有序、有使用价值的数据资源</p>\n", "<p>数据资产化:主要包括两种方式，数据信贷融资与数据证券化</p>\n", "<p>数据资产化:是数据通过流通交易给使用者或者所有者带来的经济利益的过程。数据资产化是实现数据价值的核心，其本质是形成数据交换价值，初步实现数据价值的过程</p>\n", "<p>数据资本化:是使无序、混乱的原始数据成为有序、有使用价值的数据资源</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 997, "rightCount": 658, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4321, "type": "1", "stem": "<p>【2021年下半年-第1题】-数字中国&quot;十四五&quot;期间，我国关注推动政务信息化共建共用，推动构建网络安全空间命运共同体，属于(  )建设内容。</p>\n", "answer": [1], "analysis": "<p>第五篇加快数字化发展建设数字中国迎接数字时代，激活数据要素潜能，推进网络强国建设，加快建设数字经济、数字社会数字政府，以数字化转型整体驱动生产方式、生活方式和治理方式变革。题干中&quot;推动政务信息化共建共用&quot;属于打造&quot;数字政府&quot;的内容;&quot;推动构建网络空间命运共同体&quot;属于营造良好数字生态的内容。</p>\n", "options": ["<p>科技中国</p>\n", "<p>数字中国</p>\n", "<p>制造中国</p>\n", "<p>创新中国</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 896, "rightCount": 857, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4322, "type": "1", "stem": "<p>【2022年下半年-第5题】-十四五&quot;规划&quot;十四五&quot;规划和\n2035年远景目标纲要提出，在推进产业数字化转型中，实施(  )行动，推动数据赋能全产业链协同转型。</p>\n", "answer": [0], "analysis": "<p>实施&quot;上云用数赋智&quot;行动，推动数据赋能全产业链协同转型。在重点行业和区域建设若干国际水准的工业互联网平台和数字化转型促进中心，深化研发设计、生产制造、经营管理、市场服务等环节的数字化应用，培育发展个性定制、柔性制造等新模式，加快产业园区数字化改造。深入推进服务业数字化转型，培育众包设计、智慧物流、新零售等新增长点。加快发展智慧农业，推进农业生产经营和管理服务数字化改造。</p>\n", "options": ["<p>上云用数赋智</p>\n", "<p>数字技术</p>\n", "<p>智能资源</p>\n", "<p>平台化功能</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 907, "rightCount": 819, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4323, "type": "1", "stem": "<p>【2023年上半年-第2题】-数据价值化数据价值化是以(  )为起点，经历数据资产化，数据资本化阶段，实现数据价值化的阶段。</p>\n", "answer": [1], "analysis": "<p>P20，数据价值化是指以数据资源化为起点，经历数据资产化、数据资本化阶段，实现数据价值化的经济过程上。</p>\n", "options": ["<p>数据智能化</p>\n", "<p>数据资源化</p>\n", "<p>数据安全性</p>\n", "<p>数据产业化</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 893, "rightCount": 803, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4324, "type": "1", "stem": "<p>【2023年下半年-第1批次】-数据经济以下关于数字产业化的说法中，错误的是(  )。</p>\n", "answer": [3], "analysis": "<p>P17-18，数字产业化发展重点包括:(1)云计算(2)大数据(3)物联网(4)工业互联网(5)区块链(6)人工智能(7)虚拟现实和增强现实。D错不是产业数字化，而是字产业化。</p>\n", "options": ["<p>数字产业化是指为产业数字化发展提供数字技术、产品、服务、基础设施和解决方案，以及完全依赖于数字技术、数据要素的各类经济活动，包括电子信息制造业、电信业、软件、信息技术、互联网行业等</p>\n", "<p>从产业构成上看，数字经济包括数字产业化和产业数字化两大部分</p>\n", "<p>从整体构成上看，数字经济包括数字产业化、产业数字化、数字化治理和数据价值化四个部分。</p>\n", "<p>产业数字化发展重点包括:(1)云计算(2)大数据(3)物联网(4)工业互联网(5)区块链(6)人工智能(7)虚拟现实和增强现实</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 995, "rightCount": 492, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4325, "type": "1", "stem": "<p>【2023年下半年-第1批次自编】-数字社会以下说法中，错误的是(  )。</p>\n", "answer": [3], "analysis": "<p>P25</p>\n<p>(1)数据治理:围绕数据这一新的生产要素进行能力构建，包括数据责权利管控、全生命周期管理及其开发利用等。</p>\n<p>(2)数字孪生:围绕现实世界与信息世界的互动融合进行能力构建，包括社会李生、城市李生和设备孪生等，将推动城市空间摆脱物理约束，进入数字空间。(3)边际决策:基于决策算法和信息应用等进行能力构建，强化执行端的决策能力，从而达到快速反应、高效决策的效果，满足对社会发展的敏捷需求。(4)多元融合:强调社会关系和社会活动的动态性及其融合的高效性等，实现服务可编排和快速集成，从而满足各项社会发展的创新需求。(5)态势感知:围绕对社会状态的本质反映及模拟预测等进行能力构建，洞察可变因素与不可见因素对社会发展的影响，从而提升生活质量。所以D错误。应该是态势感知，不是数据治理。</p>\n", "options": ["<p>边际决策:基于决策算法和信息应用等进行能力构建，强化执行端的决策能力，从而达到快速反应、高效决策的效果，满足对社会发展的敏捷需求。</p>\n", "<p>多元融合:强调社会关系和社会活动的动态性及其融合的高效性等，实现服务可编排和快速集成，从而满足各项社会发展的创新需求</p>\n", "<p>数字孪生:围绕现实世界与信息世界的互动融合进行能力构建，包括社会孪生、城市孪生和设备孪生等，将推动城市空间摆脱物理约束，进入数字空间。</p>\n", "<p>数据治理:围绕对社会状态的本质反映及模拟预测等进行能力构建，洞察可变因素与不可见因素对社会发展的影响，从而提升生活质量。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 974, "rightCount": 667, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 1, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4326, "type": "1", "stem": "<p>【2023年下半年-第2批次自编】-数字社会以下说法中，错误的是(  )</p>\n<p>答案:</p>\n", "answer": [], "analysis": "<p>P22，&quot;一网统管&quot;建设通常强调:</p>\n<p>(1)一网:主要包括政务云、政务网和政务大数据中心等。(2)一屏:通过对多个部门的数据进行整合，将城市运行情况充分反映出来。(3)联动:畅通各级指挥体系，为跨部门、跨区域、跨层级联勤联动、高效处置提供快速响应能力。(4)预警:基于多维、海量、全息数据汇集，实现城市运行体征的全量、实时掌握和智能预警。(5)创新:以管理需求带动智能化建设，以信息流、数据流推动业务流程全面优化和管理创新。</p>\n", "options": ["<p>&quot;一网通办&quot;是依托于一体化在线政务服务平台，通过规范网上办事标准，优化网上办事流程，搭建统一的互联网政务服务总门户，整合政府服务数据资源和完善配套制度等措施，推行政务服务事项网上办理，推动企业群众办事线上只登录一次即可全网通办。</p>\n", "<p>&quot;跨省通办&quot;是申请人在办理地之外的省市提出事项申请或在本地提出办理其他省市事项申请，办理模式通常可分为全程网办、代收代办和多地联办等。</p>\n", "<p>&quot;一网统管&quot;通常从城市治理突出问题出发，以城市事件为牵引，统筹管理网格，统一城市运行事项清单，构建多级城市运行&quot;一网统管&quot;应用体系，推动城市管理、应急指挥、综合执法等领域的&quot;一网统管&quot;，实现城市运行态势感知、体征指标监测统一事件受理、智能调度指挥、联动协同处置、监督评价考核等全流程监管。</p>\n", "<p>一网指通过对多个部门的数据进行整合，将城市运行情况充分反映出来。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 1133, "rightCount": 0, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 2, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4327, "type": "1", "stem": "<p>【2023年下半年-第3批次】-产业数字化在以下选项中，哪个不是产业数字化的典型特征(  )。</p>\n", "answer": [3], "analysis": "<p>P19。产业数字化具有的典型特征包括:以数字科技变革生产工具:</p>\n<p>·以数据资源为关键生产要素:</p>\n<p>·以数字内容重构产品结构;.</p>\n<p>·以信息网络为市场配置纽带:</p>\n<p>·以服务平台为产业生态载体;</p>\n<p>以数字善治为发展机制条件。</p>\n", "options": ["<p>以数字科技变革生产工具</p>\n", "<p>以数据资源为关键生产要素</p>\n", "<p>以信息网络为市场配置纽带</p>\n", "<p>以传统模式为主导的发展机制条件</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 887, "rightCount": 774, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4328, "type": "1", "stem": "<p>【2024年上半年-第2批次】-数字政府</p>\n<p>《&quot;十四五&quot;国家信息化规划》中提出了打造协同高效的数字政府体系，深入推进&quot;放管服&quot;改革，加快政府职能转变，打造市场化、法治化、国际化营商环境，坚持整体集约建设数字政府，推动条块政务业务协同，(  )深化推进，&quot;一网通办&quot;&quot;跨省通办&quot;&quot;一网统管&quot;，畅通参与政策制定的渠道，推动国家行政体系更加完善、政府作用更好发挥、行政效率和公信力显著提升，推动有效市场和有为政府更好结合，打造服务型政府。</p>\n", "answer": [1], "analysis": "<p>P21，《&quot;十四五&quot;国家信息化规划》中提出了打造协同高效的数字政府体系，深入推进&quot;放管服&quot;改革，加快政府职能转变，打造市场化、法治化、国际化营商环境，坚持整体集约建设数字政府，推动条块政务业务协同，加快政务数据开放共享和开发利用，深化推进，&quot;一网通办&quot;&quot;跨省通办&quot;&quot;一网统管&quot;，畅通参与政策制定的渠道，推动国家行政体系更加完善、政府作用更好发挥、行政效率和公信力显著提升，推动有效市场和有为政府更好结合打造服务型政府。</p>\n", "options": ["<p>加快政务数据资产使用便捷性</p>\n", "<p>加快政务数据开放共享和开发利用</p>\n", "<p>加快推动政务数据的价值提升和变现</p>\n", "<p>严格管控政务数据的质量和使用范围</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 904, "rightCount": 697, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4329, "type": "1", "stem": "<p>【模拟题】-元宇宙</p>\n<p>元宇宙的主要特征包括(  )①沉浸式体验②虚拟身份③虚拟经济\n④虚拟社会治理⑤虚拟人生</p>\n", "answer": [0], "analysis": "<p>P35,</p>\n<p>·沉浸式体验:元宇宙的发展主要基于人们对互联网体验的需求，这种体验就是即时信息基础上的沉浸式体验。</p>\n<p>·虚拟身份:人们已经拥有大量的互联网账号，未来人们在元宇宙中，随着账号内涵和外延的进一步丰富,将会发展成为一个或若干个数字身份,这种身份就是数字世界的一个或一组角色。·虚拟经济:虚拟身份的存在就促使元宇宙具备了开展虚拟社会活动的能力，而这些活动需要一定的经济模式展开，即虚拟经济。·虚拟社会治理:元宇宙中的经济与社会活动也需要一定的法律法规和规则的约束，就像现实世界一样，元宇宙也需要社区化的社会治理</p>\n", "options": ["<p>①②③④</p>\n", "<p>①③④⑤</p>\n", "<p>②③④⑤</p>\n", "<p>①②④⑤</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 898, "rightCount": 736, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4330, "type": "1", "stem": "<p>【2022年上半年-第7题】-元宇宙元宇宙本身不是一种技术，而是一个理念和概念，它需要整合不同的新技术，强调虚实相融元宇宙主要有以下几项核心技术:一是(  )，包括VR、AR和MR，可以提供沉浸式的体验;是(  )，能够把现实世界镜像到虚拟世界里面去，在元字宙里面，我们可以看到很多自己的虚拟分身;三是用(  )来搭建经济体系。经济体系将通过稳定的虚拟产权和成熟的去中心化金融生态具备现实世界的调节功能，市场将决定用户劳动创造的虚拟价值。</p>\n", "answer": [0], "analysis": "<p>元宇宙主要有以下几项核心技术:</p>\n<p>一是扩展现实技术，包括VR和AR。扩展现实技术可以提供沉浸式的体验，可以解决手机解决不了的问题。二是数字孪生，能够把现实世界镜像到虚拟世界里面去。这也意味着在元宇宙里面，我们可以看到很多自己的虚拟分身。</p>\n<p>三是用区块链来搭建经济体系。随着元宇宙进一步发展，对整个现实社会的模拟程度加强，我们在元宇宙当中可能不仅仅是在花钱，而且有可能赚钱,这样在虚拟世界里同样形成了一套经济体系。</p>\n<p>【点评】VR和AR最大的区别就在于，VR是虚拟现实而AR是增强现实。通俗一点来说就是分别戴上\nVR和AR 眼镜，戴上 VR眼镜看到的都是假的、虚构的，而戴上 AR\n眼镜看到的是真实存在的场景AR是将虚拟信息加在真实环境中，来增强真实环境，因此看到的图像是半真半假。MR是将真实世界和虚拟世界混合在一起，可以说它呈现的图像令人真假难辨。MR比较像是VR和\nAR的组合，可以在现实的场景中显示立体感十足的虚拟图像，且还能通过双手和虚拟图像进行交互。VR、AR和MR它们的从属关系是这样的:VR概念最小，AR概念包含了VR，MR概念最大，包含了VR和AR。</p>\n", "options": ["<p>扩展现实、数字孪生、区块链</p>\n", "<p>增强现实、虚拟技术、区块链</p>\n", "<p>增强现实、数字孪生、大数据</p>\n", "<p>扩展现实、虚拟技术、大数据</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 956, "rightCount": 571, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 0, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}, {"id": 4331, "type": "1", "stem": "<p>【2023年下半年-第1批次自编】-数字转型</p>\n<p>以下关于数字化转型的说法，错误的是(  )。</p>\n", "answer": [2], "analysis": "<p>P33，&quot;数据-智慧&quot;过程该过程通常指数据的开发利用和资源管理的过程，即人们常说的&quot;智慧化过程&quot;，重点解决基于各类组织组成对象&quot;数字关系&quot;的&quot;脑力替代&quot;。该过程在大数据&quot;筑底&quot;后，多元化数据能够被开发利用:①通过对象数字化实现对各类对象的数字化表达;②)通过李生虚拟化完成物理对象到信息空间的映射;所以C错③通过架构可视化实现业务知识模型与经验沉淀的复用和创新;④通过计算智能化实现多元条件下的调度和决策。P658，数字化转型组织架构及工作机制的建议可分为4个层次:·规划层:顶层设计、具有全局观。</p>\n<p>实施层:围绕数字化产品和服务进行实施推进</p>\n<p>能力层:构建数字化相关的支撑实施层的能力。·资源层:组织与传统业务、传统IT链接</p>\n<p>所以B对。</p>\n<p>P657，常见数字化转型的驱动因素主要包括:。新技术的强势发展:新兴的技术让组织的运行、研发、设计、生产、服务等每个环节能够通过网络实现无缝对接，以及不同地区的员工可以利用在线方式进行顺利的交流沟通，强化新技术应用是所有组织重点考虑的内容之一。</p>\n<p>·低&quot;交互成本&quot;运作:使用数字化手段逐渐将员工从大量重复性工作中解放出来，节省时间及人力物力，提升业务处理的效率，并通过数据的共享交换和算法的开发利用，驱动组织敏捷协同和立体化协同。</p>\n<p>业务运行的透明化:迅速发展的数字化潮流，逐渐改变了组织的业务模式和管理模式等，大多数组织数字化已经渗透到其每一个业务环节中，从而能够使组织各项工作通过数据进行表达，使工作干系人能够清晰地了解工作的进展情况、遇到的困难、决策的原则获得的结果驱动组织高效和透明地运作。</p>\n<p>·个性化需求的满足:数字化促使组织产品和服务满足不同对象的个性化需求，个性化需求的满足是实现日益增长的人民对美好生活需要的重要举措,其决定的是未来组织的发展战略方向。这需要组织通过研究与预测客户和服务对象的需求,提供充分满足其需求的产品和服务。所以A对。</p>\n", "options": ["<p>常见数字化转型的驱动因素主要包括:(1)新技术的强势发展(2)低&quot;交互成本&quot;运作(3)业务运行的透明化(4)个性化需求的满足</p>\n", "<p>数字化转型组织架构及工作机制的建议可分为4个层次:(1)规划层(2)实施层(3)能力层(4)资源层</p>\n", "<p>通过计算智能化完成物理对象到信息空间的映射</p>\n", "<p>数字化转型基本原理揭示了个体智慧由&quot;自然人&quot;个体，转移到组织智慧(计算机、信息系统等掌握的)的必要性和重要性。</p>\n"], "difficulty": 2, "parentId": 0, "subCount": 0, "nature": 3, "score": 1, "kind": "", "source": "", "categoryId": 0, "containCount": 0, "noteCounts": 0, "studCount": 987, "rightCount": 534, "meanScoreRate": 0, "qBankId": 5, "chapterId": 76, "mediaId": 0, "oldMediaId": 0, "mediaTable": "old", "stemMediaId": 0, "baseId": 0, "kindId": 0, "seq": 0, "postNum": 3, "pType": "1", "isHaveAnalysisText": 1, "isExistTextAnalysis": 1, "isHaveAnalysisVideo": 1, "isExistVideoAnalysis": 0, "isDid": -1, "isfavorite": -1, "stemMedia": {"mediaType": "", "mediaUri": "", "mediaTime": "", "videoPictureUri": "", "definition": {}, "formatStream": []}, "analysisVideoThumb": "", "qNo": null}], "title": "第一章 信息化发展", "lastQNo": 0, "lastQId": 0, "optional": {"singleQShowType": 2, "imgNoEvents": 0, "qEncrypt": {"switch": 0, "key": ""}, "deflate": 0, "questionComment": 1, "serverTime": *************, "ad": {}, "wltest": 2, "showStructure": 0}}, "errno": 0, "errmsg": ""}