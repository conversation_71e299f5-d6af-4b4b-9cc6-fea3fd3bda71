const crypto = require('crypto');
const https = require('https');
const fs = require('fs');

/**
 * 解密CryptoJS格式的AES加密数据
 * @param {string} encryptedData - Base64编码的加密数据
 * @param {string} password - 解密密码
 * @returns {string|null} 解密后的数据
 */
function decryptCryptoJS(encryptedData, password) {
    try {
        // 解码base64
        const encrypted = Buffer.from(encryptedData, 'base64');
        
        // 检查是否是CryptoJS格式（以"Salted__"开头）
        if (encrypted.subarray(0, 8).toString() !== 'Salted__') {
            throw new Error('不是有效的CryptoJS格式');
        }
        
        // 提取盐（8字节）
        const salt = encrypted.subarray(8, 16);
        
        // 提取加密数据
        const ciphertext = encrypted.subarray(16);
        
        // 使用EVP_BytesToKey算法派生密钥和IV（模拟CryptoJS的行为）
        const keyIv = evpBytesToKey(password, salt, 32, 16);
        const key = keyIv.key;
        const iv = keyIv.iv;
        
        // 解密
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        decipher.setAutoPadding(true);
        
        let decrypted = decipher.update(ciphertext);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        
        return decrypted.toString('utf8');
    } catch (error) {
        console.error('CryptoJS解密失败:', error.message);
        return null;
    }
}

/**
 * EVP_BytesToKey算法实现（用于兼容CryptoJS）
 * @param {string} password - 密码
 * @param {Buffer} salt - 盐
 * @param {number} keyLen - 密钥长度
 * @param {number} ivLen - IV长度
 * @returns {object} 包含key和iv的对象
 */
function evpBytesToKey(password, salt, keyLen, ivLen) {
    const targetKeySize = keyLen + ivLen;
    const derivedBytes = Buffer.alloc(targetKeySize);
    let numberOfDerivedWords = 0;
    let block = null;
    
    while (numberOfDerivedWords < targetKeySize) {
        const hasher = crypto.createHash('md5');
        
        if (block) {
            hasher.update(block);
        }
        
        hasher.update(password, 'utf8');
        hasher.update(salt);
        
        block = hasher.digest();
        block.copy(derivedBytes, numberOfDerivedWords);
        numberOfDerivedWords += block.length;
    }
    
    return {
        key: derivedBytes.subarray(0, keyLen),
        iv: derivedBytes.subarray(keyLen, keyLen + ivLen)
    };
}

/**
 * 获取并解密题目数据
 */
async function fetchAndDecryptQuestions() {
    const url = 'https://api.ixunke.cn/yuyiruankao/api/v1/question/sequence_practise_nestification?app=true&token=**************************************************************************************************************************************************************************%3D%3D&qBankId=4&chapterId=62&studentAnswer=1';
    
    return new Promise((resolve, reject) => {
        console.log('正在获取数据...');
        
        https.get(url, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    console.log('数据获取成功');
                    
                    // 提取加密的题目数据
                    const encryptedQuestions = jsonData.data.questions;
                    
                    // 提取加密密钥
                    const encryptConfig = jsonData.data.optional.qEncrypt;
                    const encryptedKey = encryptConfig.key;
                    
                    console.log('加密密钥:', encryptedKey);
                    console.log('正在尝试解密密钥...');
                    
                    // 尝试解密密钥的常见密码
                    const possiblePasswords = [
                        'yuyiruankao',
                        'ixunke',
                        'questions',
                        'qbank',
                        'default',
                        '123456',
                        'password',
                        'ruankao',
                        'exam',
                        'test'
                    ];
                    
                    let decryptedKey = null;
                    for (const pwd of possiblePasswords) {
                        const result = decryptCryptoJS(encryptedKey, pwd);
                        if (result) {
                            console.log(`密钥解密成功，使用密码: ${pwd}`);
                            console.log(`解密后的密钥: ${result}`);
                            decryptedKey = result;
                            break;
                        }
                    }
                    
                    if (!decryptedKey) {
                        console.log('无法解密密钥，尝试直接使用加密密钥...');
                        decryptedKey = encryptedKey;
                    }
                    
                    console.log('正在尝试解密题目数据...');
                    
                    let decryptedQuestions = null;
                    
                    // 方法1: 使用解密后的密钥
                    if (decryptedKey && decryptedKey !== encryptedKey) {
                        decryptedQuestions = decryptCryptoJS(encryptedQuestions, decryptedKey);
                    }
                    
                    // 方法2: 如果方法1失败，尝试使用原始密钥
                    if (!decryptedQuestions) {
                        decryptedQuestions = decryptCryptoJS(encryptedQuestions, encryptedKey);
                    }
                    
                    // 方法3: 尝试常见密码
                    if (!decryptedQuestions) {
                        for (const pwd of possiblePasswords) {
                            decryptedQuestions = decryptCryptoJS(encryptedQuestions, pwd);
                            if (decryptedQuestions) {
                                console.log(`题目解密成功，使用密码: ${pwd}`);
                                break;
                            }
                        }
                    }
                    
                    if (decryptedQuestions) {
                        console.log('题目数据解密成功！');
                        try {
                            // 尝试解析为JSON
                            const questionsData = JSON.parse(decryptedQuestions);
                            
                            // 保存解密后的数据
                            const outputFile = 'decrypted_questions.json';
                            fs.writeFileSync(outputFile, JSON.stringify(questionsData, null, 2), 'utf8');
                            
                            console.log(`解密后的数据已保存到: ${outputFile}`);
                            console.log(`题目数量: ${Array.isArray(questionsData) ? questionsData.length : '未知'}`);
                            
                            resolve(questionsData);
                            
                        } catch (jsonError) {
                            console.error('解密后的数据不是有效的JSON格式:', jsonError.message);
                            // 保存为文本文件
                            fs.writeFileSync('decrypted_questions.txt', decryptedQuestions, 'utf8');
                            console.log('解密后的数据已保存为文本文件: decrypted_questions.txt');
                            resolve(decryptedQuestions);
                        }
                    } else {
                        console.log('题目数据解密失败');
                        reject(new Error('解密失败'));
                    }
                    
                } catch (error) {
                    console.error('解析响应数据失败:', error.message);
                    reject(error);
                }
            });
            
        }).on('error', (error) => {
            console.error('请求失败:', error.message);
            reject(error);
        });
    });
}

// 运行解密程序
fetchAndDecryptQuestions()
    .then((result) => {
        console.log('解密完成！');
    })
    .catch((error) => {
        console.error('解密失败:', error.message);
    });
